from fastapi import (
    <PERSON><PERSON><PERSON><PERSON>,
    Header,
    Form,
    Query,
    HTTPException,
    Depends
)
from fastapi.responses import JSONResponse
from uuid import UUID
from app.services.migrate.migration_processor import MigrateProcessor
from app.schemas.migrate import WorkbooksRequest
from app.models.users import User
from app.models_old.user import UserOld
from app.models.report_details import ReportDetailManager
from app.core.dependencies import get_current_user
from app.core.constants import MIGRATE_OUTPUTS_PATH
from app.core.config import S3Config
from app.core import logger


migrate_router = APIRouter()

@migrate_router.post("/tableau-to-powerbi/{report_id}")
async def migrate_report_to_powerbi(
    report_id: UUID,
    user: User = Depends(get_current_user)
):
    """
    API for migrating a single report from Tableau to Power BI.
    """
    logger.info(f"[Migration API] Starting migration for report_id: {report_id}, user: {user.email}")

    try:
        response = await MigrateProcessor.migrate_single_report(report_id, user)
        
        logger.info(f"[Migration API] Migration completed successfully for report_id: {report_id}")
        return {
            "message": "Migration completed successfully",
            "data": response.data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[Migration API] Migration failed for report_id: {report_id}, error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Migration failed: {str(e)}")
