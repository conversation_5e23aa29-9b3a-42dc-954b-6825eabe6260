from fastapi import APIRouter, Form, HTTPException, status, Depends, Query, Body
from fastapi.responses import JSONResponse
from uuid import UUID

from app.models.users import User
from app.services.analysis import AnalysisProcessor
from app.core.dependencies import get_current_user
from app.schemas.analyse import SuccessResponse


analysis_router = APIRouter()   




@analysis_router.post("/generate_report/{report_id}", response_model=SuccessResponse)
async def generate_report_analysis(
    report_id: UUID,
    user: User = Depends(get_current_user)
):
    """
    Triggers report analysis or returns pre-signed URL if already analyzed.

    Args:
        report_id (UUID): ID of the report from report_details table.
        user (User): Authenticated user.

    Returns:
        SuccessResponse: Contains download link and report path.
    """
    response = await AnalysisProcessor.analyse_processor(report_id, user)
    
    # Convert ServiceResponse to SuccessResponse format
    return {
        "message": response.error or ("Success" if response.status_code == 200 else "Failed"),
        "data": response.data
    }
