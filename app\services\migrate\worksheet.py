from .core import get_tables_data
from .visuals import (
    get_treemap_report, get_filledmap_report, get_card_report,get_linechart_report,
    get_map_report, get_table_report, get_piechart_report, 
    get_line_stacked_report, get_barchart_report,get_scatter_plot,get_areachart_report,
    get_highlight_report,get_stacked_bar_report,process_text_box_in_worksheets, get_title_textbox,
    get_line_bar_chart_report
)

from app.core.templates import *
from app.core.logger_setup import logger
from app.core.enums import ChartType

def get_worksheet_names(worksheets):
    worksheet_names = [worksheet.get('@name') for worksheet in worksheets]
    return worksheet_names

def get_config_json(data, chart_types):
    worksheets = data.get('workbook').get('worksheets').get('worksheet')
    worksheets = worksheets if isinstance(worksheets, list) else [worksheets]
    table_column_data = get_tables_data(data)
    chart_types_data = {chart.get("name"): chart.get("chart_type") for chart in chart_types}
    worksheet_data = {}
    
    for worksheet in worksheets:
        pane_class = None
        pane_encodings = None
        worksheet_name = worksheet.get('@name')
        visual_type = chart_types_data.get(worksheet_name)
        logger.info(f"Processing worksheet {worksheet_name}")
        panes = worksheet.get('table',{}).get('panes',{}).get('pane')
        measures_filter = worksheet.get('table',{}).get('view',{}).get('filter')
        computed_sort = worksheet.get('table',{}).get('view',{}).get('computed-sort')
        if panes and isinstance(panes, dict): 
            pane_class = panes.get('mark',{}).get('@class')
            pane_encodings = panes.get('encodings',{})
        style = worksheet.get('table',{}).get('style')
        datasources = worksheet.get('table',{}).get('view',{}).get('datasource-dependencies')
        datasources_list = datasources if isinstance(datasources, list) else [datasources]
        datasource_column_list = []
        if datasources_list:
            for datasource in datasources_list:
                if datasource is not None:
                    datasource_column_list.extend(datasource.get('column'))
        worksheet_title_layout = worksheet.get('layout-options',{}).get('title',{}).get('formatted-text',{}).get('run')
        rows = worksheet.get('table',{}).get('rows')
        cols = worksheet.get('table',{}).get('cols')

        if visual_type == ChartType.CARDS.value:
            card_result = get_card_report(measures_filter, table_column_data, datasource_column_list, worksheet_name, style)
            worksheet_data[worksheet_name] = {"visual_type":"card", "visual_content": card_result}
            if worksheet_title_layout:
                card_title_result = get_title_textbox(worksheet_title_layout, style, is_dashboard = False)
                if card_title_result:
                    worksheet_data[worksheet_name]["card_title"] = card_title_result
            logger.info(f"Completed generating cards for {worksheet_name}")

        elif visual_type == ChartType.TREE_MAP.value:
            treemap_result = get_treemap_report(pane_encodings,worksheet_name,table_column_data, worksheet_title_layout, style)
            logger.info(f"Completed generating treemap for {worksheet_name}")
            worksheet_data[worksheet_name] = treemap_result

        elif visual_type == ChartType.FILLED_MAP.value:
            filledmap_result = get_filledmap_report(panes,table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            logger.info(f"Completed generating filledmap for {worksheet_name}")
            worksheet_data[worksheet_name] = filledmap_result

        elif visual_type == ChartType.PIE.value and pane_encodings:
            piechart_result = get_piechart_report(pane_encodings, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            if piechart_result:
                worksheet_data[worksheet_name] = piechart_result
                logger.info(f"Completed generating pie chart for {worksheet_name}")

        elif visual_type == ChartType.TEXT_TABLE.value:
            table_result = get_table_report(rows, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            worksheet_data[worksheet_name] = table_result
            logger.info(f"Completed generating table for {worksheet_name}")

        elif visual_type == ChartType.LINE.value and rows and cols and panes:
            line_result = get_linechart_report(rows, cols, table_column_data, panes, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            if line_result:
                worksheet_data[worksheet_name] = line_result
                logger.info(f"Completed generating line chart for {worksheet_name}")

        elif visual_type == ChartType.SYMBOL_MAP.value and pane_encodings and rows and cols:
            map_result = get_map_report(pane_encodings, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            if map_result:
                worksheet_data[worksheet_name] = map_result
                logger.info(f"Completed generating map for {worksheet_name}")

        elif visual_type == ChartType.BAR_AND_AREA.value and cols:
            line_stacked_result = get_line_stacked_report(panes, cols, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            if line_stacked_result:
                worksheet_data[worksheet_name] = line_stacked_result
                logger.info(f"Completed generating line stacked column for {worksheet_name}")

        elif visual_type in [ChartType.HORIZONTAL_BAR.value, ChartType.VERTICAL_BAR.value] and rows and cols:
            barchart_result = get_barchart_report(rows, cols, visual_type, panes, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            if barchart_result:
                worksheet_data[worksheet_name] = barchart_result
                logger.info(f"Completed generating bar chart for {worksheet_name}")

        elif visual_type == ChartType.SCATTER.value or pane_class == "Scatter":
            scatterplot_result = get_scatter_plot(rows, cols, panes, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style)
            worksheet_data[worksheet_name] = scatterplot_result
            logger.info(f"Completed generating scatter plot for {worksheet_name}")

        elif visual_type == ChartType.AREA.value or pane_class == "Area":
            orderby=worksheet.get("table",{}).get("view",{}).get("computed-sort",{})
            areaplot_result=get_areachart_report(rows,cols,pane_encodings,table_column_data,datasource_column_list,orderby, worksheet_name, worksheet_title_layout, style)
            worksheet_data[worksheet_name] = areaplot_result
            logger.info(f"Completed generating area chart for {worksheet_name}")

        elif visual_type == ChartType.HIGHLIGHTED_TABLE.value or visual_type == ChartType.PIVOT_TABLE.value or pane_class == "Square":
            values=pane_encodings.get("color", {}).get("@column")
            highlightjson=get_highlight_report(rows,cols,visual_type,pane_encodings,table_column_data,datasource_column_list,values, worksheet_name, worksheet_title_layout, style)
            worksheet_data[worksheet_name] = highlightjson
            logger.info(f"Completed generating Highlight chart for {worksheet_name}")

        elif visual_type == ChartType.STACKED_VERTICAL_BAR.value:
            orderby=worksheet.get("table",{}).get("view",{}).get("computed-sort",{}) or worksheet.get("table",{}).get("view",{}).get("natural-sort",{})
            stackedbar_result=get_stacked_bar_report(rows,cols,pane_encodings,table_column_data,datasource_column_list,orderby, style, worksheet_title_layout, worksheet_name,"V")
            worksheet_data[worksheet_name] = stackedbar_result
            logger.info(f"Completed generating bar chart for {worksheet_name}")

        elif visual_type == ChartType.BAR_AND_LINE.value:
            styles_rules=worksheet.get("table",{}).get("style").get("style-rule")
            cat_extra_axis=""
            linebar_json=get_line_bar_chart_report(rows,cols,table_column_data,datasource_column_list,cat_extra_axis,worksheet_name)
            worksheet_data[worksheet_name]=linebar_json
            logger.info(f"Completed generating Line bar chart for {worksheet_name}")

        elif visual_type == ChartType.STACKED_HORIZONTAL_BAR.value:
            orderby=worksheet.get("table",{}).get("view",{}).get("computed-sort",{})
            stackedbar_horizontal_result=get_stacked_bar_report(cols,rows,pane_encodings,table_column_data,datasource_column_list,orderby,style,worksheet_title_layout, worksheet_name,"H")
            worksheet_data[worksheet_name] = stackedbar_horizontal_result
            logger.info(f"Completed generating bar chart for {worksheet_name}")

        elif visual_type == ChartType.TEXT_BOX.value and  pane_encodings and (rows or cols):
            text_box_json=process_text_box_in_worksheets(rows,cols, worksheet_name, worksheet, textbox_type = "textbox")
            worksheet_data[worksheet_name]=text_box_json
            logger.info(f"Completed generating Text box for {worksheet_name}")
        else:
            custom_json=process_text_box_in_worksheets(rows,cols, worksheet_name,worksheet)
            worksheet_data[worksheet_name]=custom_json
            logger.info(f" Completed generating Custom visuals for {worksheet_name}")
    return worksheet_data

def get_worksheet_json(data, chart_types):
    logger.info(f"========Started worksheet migration========")
    worksheet_data = get_config_json(data, chart_types)
    overall_worksheet_result = {}
    worksheet_dimensions = {
        "height": 720, "width": 1280, "x":0.00, "y" : 0.00, "z": 0
    }
    for ws_name,content in worksheet_data.items():
        if isinstance(content, list):
            config_list = []
            for item in content:
                template = item.get("template")
                config = item.get("config")
                result = template.format(**config, **worksheet_dimensions)
                config_list.append({"config": result, "filters":"[]", **worksheet_dimensions})
            overall_worksheet_result[ws_name] = config_list
        else:
            visual_content = content.get("visual_content")
                
            no_of_cards = len(visual_content)
            base_x = worksheet_dimensions.get("x", 0)
            base_y = worksheet_dimensions.get("y", 0)
            base_width = worksheet_dimensions.get("width", 1000)
            base_height = 80
            margin = 10  # Space between cards

            card_config_list = []
            card_title = content.get("card_title")
            if card_title:
                card_title_template = card_title.get("template")
                card_title_config = card_title.get("config")
                card_title_dimensions = {
                    "x": base_x,
                    "y": base_y,
                    "width": base_width,
                    "height": 50,
                    "z":0.0
                }
                card_title_result = card_title_template.format(**card_title_config, **card_title_dimensions)
                card_config_list.append({"config": card_title_result, "filters":"[]", **card_title_dimensions})
            # Calculate card width
            card_width = (base_width - (margin * (no_of_cards - 1))) / no_of_cards
            for idx, card_content in enumerate(visual_content):
                card_template = card_content.get("template")
                card_config = card_content.get("config")
                card_x = base_x + idx * (card_width + margin)
                card_y = base_y + 50
                card_dimensions = {
                    "x": card_x,
                    "y": card_y,
                    "width": card_width,
                    "height": base_height,
                    "z":0.0
                }
                card_result = card_template.format(**card_config, **card_dimensions)
                card_config_list.append({"config": card_result, "filters":"[]", **card_dimensions})
            overall_worksheet_result[ws_name] = card_config_list
    return overall_worksheet_result