from app.core import filledmap_template
from ..core import (get_background_config, get_border_config, get_title_config, process_pane_encodings,
                    process_tooltip_data, get_projections_data, process_multiple_fields, to_list,
                    get_from_list, create_expr, process_label_data, process_format_data,
                    get_color_formatting)
from app.core import logger
import uuid, json
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_multiple_encodings_data,
    generate_projections_data
)
from app.core.enums import (
    PowerBITemplateKeys, PowerBIReportKeys, PowerBIChartTypes, VisualRequest, TableauXMLTags
)


def process_filledmap_colors(color_field, panes, style, table_column_data, datasource_column_list):
    if color_field:
        values_input, value_queryref, min_color, mid_color, max_color = get_color_formatting(style, color_field, table_column_data, datasource_column_list)
        return {"properties":{"fill": {"solid": {"color": {"expr": {"FillRule": {"Input": values_input,"FillRule": {"linearGradient3": {"min": {"color": {"Literal": {"Value": f"'{min_color}'"}}},"mid": {"color": {"Literal": {"Value": f"'{mid_color}'"}}},"max": {"color": {"Literal": {"Value": f"'{max_color}'"}}},"nullColoringStrategy": {"strategy": {"Literal": {"Value": "'asZero'"}}}}}}}}}}},"selector": {"data": [{"dataViewWildcard": {"matchingOption": 1}}]}}
    if panes:
        panes_data = to_list(panes)
        style_rule_data = panes_data[0].get("style",{}).get("style-rule")
        if style_rule_data:
            style_rule_data = to_list(style_rule_data)
            for data in style_rule_data:
                if data.get("@element") == "mark":
                    format_data = data.get("format")
                    format_data = to_list(format_data)
                    for item in format_data:
                        if item.get("@attr") == "mark-color":
                            color = item.get("@value")
                            return {"properties":{"fill":{"solid":{"color":create_expr(f"'{color}'")}}}}
                        
    return {"properties":{"fill":{"solid":{"color":create_expr("'#4e79a7'")}}}}

def get_filledmap_objects_data(color_field, panes, style, table_column_data, datasource_column_list):
    objects = {}
    objects["mapStyles"] = [{"properties": {"mapTheme": create_expr("'canvasLight'"), "showLabels" : create_expr("true")}}]
    datapoint_details = process_filledmap_colors(color_field, panes, style, table_column_data, datasource_column_list)
    objects["dataPoint"] = [datapoint_details]
    objects["mapControls"] = [{"properties":{"autoZoom": create_expr("true"), "showZoomButtons" : create_expr("false"), "showLassoButton" : create_expr("false")}}]
    return objects

def get_filledmap_report(panes ,table_column_data,datasource_column_list, worksheet_name, worksheet_title_layout, style):
    try:
        overall_filledmap_result = []

        projections_dict = {}

        color_field, tooltips, lod_data, text_data = process_pane_encodings(panes)
        tooltips_data = process_tooltip_data(data= [tooltips, text_data], color_field= color_field)

        lod_coloumn_data = [data.get("@column") for data in lod_data]
        final_lod_data = process_multiple_fields(lod_coloumn_data)

        result_queryref, table_list, select_list = get_projections_data([final_lod_data, tooltips_data], table_column_data, datasource_column_list)

        location_data = result_queryref.get(final_lod_data)
        tooltip_queryref_list = result_queryref.get(tooltips_data)

        projections_dict["Category"] = location_data
        if tooltip_queryref_list: projections_dict["Tooltips"] = tooltip_queryref_list

        from_list = get_from_list(table_list)

        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)

        objects_data = get_filledmap_objects_data(color_field, panes, style, table_column_data, datasource_column_list)

        filledmap_json_data = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "projection_data" : json.dumps(projections_dict),
            "from_list" : json.dumps(from_list),
            "select_list" : json.dumps(select_list),
            "objects_data" : json.dumps(objects_data),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }

        overall_filledmap_result.append({"config": filledmap_json_data, "template": filledmap_template})

        return overall_filledmap_result
    except Exception as e:
        logger.error(f"---Error in generating filled map visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating filled map visual for {worksheet_name} - {str(e)}")
    

def process_filled_map_report(request: VisualRequest):
    """
    Process filled map chart visual request.
    
    Parameters
    ----------
    request : VisualRequest
        The request object containing details for the filled map chart.
    
    Returns
    -------
    list
        A list containing the processed filled map chart data.
    """
    filled_map_results = {}
    panes = request.panes
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data
    panes = request.panes

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )

    unique_lod_data = remove_duplicate_fields(encodings[TableauXMLTags.LOD.value])
    unique_tooltips= remove_duplicate_fields(encodings[TableauXMLTags.TOOLTIP.value])

    filled_map_field_mapping = {
        PowerBIReportKeys.CATEGORY.value: unique_lod_data
    }
    if unique_tooltips:
        filled_map_field_mapping[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips

    projections_data = generate_projections_data(table_column_data, calculations_related_data, filled_map_field_mapping)
    
    filled_map_results[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.FILLED_MAP.value
    filled_map_results[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    filled_map_results[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data
    return filled_map_results