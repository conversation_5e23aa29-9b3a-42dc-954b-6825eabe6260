import uuid
from app.core.enums import Datasource, PowerBITemplateKeys

async def fetch_objects_data(datasource):
    objects = datasource.findall(Datasource.OBJECT_TAG.value)
    objects_data = {}
    for obj in objects:
        caption = obj.get(Datasource.CAPTION.value)
        object_id = obj.get(Datasource.ID.value)
        if caption and object_id:
            objects_data[object_id] = caption
    return objects_data

async def fetch_table_columns_data(datasources):
    table_column_data = {}

    for datasource in datasources:
        tables_data = {}
        datasource_name = datasource.get(Datasource.NAME.value)
        if datasource_name == "Parameters":
            continue

        named_conn = datasource.find(Datasource.NAMED_CONNECTIONS.value)
        connection_type = ""
        directory = ""
        server = ""
        database = ""

        if named_conn is not None:
            named_connection = named_conn.find("named-connection")
            if named_connection is not None:
                inner_conn = named_connection.find("connection")
                if inner_conn is not None:
                    connection_type = inner_conn.get(Datasource.CONNECTION_CLASS.value, "")
                    if connection_type == "textscan":
                        directory = inner_conn.get(Datasource.CONNECTION_DIRECTORY.value, "")
                    elif connection_type == "excel-direct":
                        directory = inner_conn.get("filename", "")
                    elif connection_type == "sqlserver":
                        database = inner_conn.attrib.get("dbname")
                        server = inner_conn.attrib.get("server")

        # Parse column metadata  which is in datasource tag not in extract
        metadata_records = datasource.find("connection").findall("metadata-records/metadata-record") if datasource.find("connection") is not None else []
        objects_data = await fetch_objects_data(datasource)
        table_column_data.setdefault(datasource_name, {})

        for record in metadata_records:

            # fetch data where meta data class is column
            if record.attrib.get("class") != "column":
                continue  

            remote_name = record.findtext(Datasource.REMOTE_NAME.value)
            local_type = record.findtext(Datasource.LOCAL_TYPE.value)
            aggregation = record.findtext(Datasource.AGGREGATION.value, "none")
            parent_name = record.findtext(Datasource.PARENT_NAME.value).strip("[]")

            object_id = next((elem.text for elem in record if elem.tag.endswith(Datasource.OBJECT_ID.value)), None)
            object_id = object_id.strip("[]") if object_id else None

            table_name = objects_data.get(object_id)
            table_name = table_name if table_name else parent_name
            if table_name.lower().endswith(".csv"):
                table_name = table_name[:-4]

            tables_data.setdefault(table_name, {})
            tables_data[table_name].setdefault(PowerBITemplateKeys.UNIQUE_ID.value, str(uuid.uuid4()))
            tables_data[table_name].setdefault(PowerBITemplateKeys.COLUMN_DATA.value, [])
            tables_data[table_name].setdefault(PowerBITemplateKeys.CONNECTION_TYPE.value, connection_type)
            tables_data[table_name].setdefault(PowerBITemplateKeys.DIRECTORY.value, directory)
            tables_data[table_name].setdefault(PowerBITemplateKeys.SERVER.value, server)
            tables_data[table_name].setdefault(PowerBITemplateKeys.DATABASE.value, database)

            tables_data[table_name][PowerBITemplateKeys.COLUMN_DATA.value].append({
                PowerBITemplateKeys.DATA_TYPE.value: local_type,
                PowerBITemplateKeys.AGGREGATION.value: aggregation,
                PowerBITemplateKeys.COLUMN_NAME.value: remote_name,
                PowerBITemplateKeys.UNIQUE_ID.value: str(uuid.uuid4()),
            })

        table_column_data[datasource_name] = tables_data

    return table_column_data
