from ..core import *
import json, uuid
from app.core.enums import (
    ChartType, PowerBITemplateKeys, TableauXMLTags,
    PowerBIReportKeys, PowerBIChartTypes, VisualRequest
)
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_multiple_encodings_data,
    generate_projections_data
)


def process_bar_colors(color_field, panes, style, table_column_data, datasource_column_list):
    if color_field:
        values_input, value_queryref, min_color, mid_color, max_color = get_color_formatting(style, color_field, table_column_data, datasource_column_list)
        return {"properties":{"fill": {"solid": {"color": {"expr": {"FillRule": {"Input": values_input,"FillRule": {"linearGradient3": {"min": {"color": {"Literal": {"Value": f"'{min_color}'"}}},"mid": {"color": {"Literal": {"Value": f"'{mid_color}'"}}},"max": {"color": {"Literal": {"Value": f"'{max_color}'"}}},"nullColoringStrategy": {"strategy": {"Literal": {"Value": "'asZero'"}}}}}}}}}}},"selector": {"data": [{"dataViewWildcard": {"matchingOption": 1}}]}}
    if panes:
        panes_data = to_list(panes)
        style_rule_data = panes_data[0].get("style",{}).get("style-rule")
        if style_rule_data:
            style_rule_data = to_list(style_rule_data)
            for data in style_rule_data:
                if data.get("@element") == "mark":
                    format_data = data.get("format")
                    format_data = to_list(format_data)
                    for item in format_data:
                        if item.get("@attr") == "mark-color":
                            color = item.get("@value")
                            return {"properties":{"fill":{"solid":{"color":create_expr(f"'{color}'")}}}}
                        
    return {"properties":{"fill":{"solid":{"color":create_expr("'#4e79a7'")}}}}

def get_barchart_objects_data(color_field, panes, style, visual_type, rows, cols, table_column_data, datasource_column_list):
    objects = {}
    objects["lineStyles"] = [{"properties": {"showMarker": {"expr": {"Literal": {"Value": "true"}}}}}]
    style_rule = to_list(style.get("style-rule")) if style else None
    objects["legend"] = [{"properties":{"show":create_expr("false")}}]
    label_data = process_label_data(style_rule)
    datapoint_details = process_bar_colors(color_field, panes, style, table_column_data, datasource_column_list)
    objects["dataPoint"] = [datapoint_details]
    if label_data: objects["labels"] = [{"properties":label_data}]
    if style_rule:
        if visual_type == ChartType.HORIZONTAL_BAR.value:
            value_axis = process_format_data(style_rule, cols, "cols")
            category_axis = process_format_data(style_rule, rows, "rows")
        else:
            value_axis = process_format_data(style_rule, rows, "rows")
            category_axis = process_format_data(style_rule, cols, "cols")
        if value_axis: objects["valueAxis"] = [{"properties":value_axis}]
        if category_axis: objects["categoryAxis"] = [{"properties":category_axis}]
    return objects

def get_stacked_bar_horizontal_report(rows, cols, visual_type, panes, table_column_data, datasource_column_list, worksheet_name, worksheet_title_layout, style):
    
    try:
        overall_barchart_result = []

        projections_dict = {}

        color_field, tooltips, lod_data, text_data = process_pane_encodings(panes)

        tooltips_data = process_tooltip_data(data = [tooltips, lod_data, text_data], visual_fields= [rows, cols])

        result_queryref, table_list, select_list = get_projections_data([rows, cols, tooltips_data], table_column_data, datasource_column_list)
        if visual_type == ChartType.HORIZONTAL_BAR.value:
            bar_visual_type = "clusteredBarChart"
            y_queryref_list = result_queryref[cols]
            category_queryref_list = result_queryref[rows]
        else:
            bar_visual_type = "clusteredColumnChart"
            y_queryref_list = result_queryref[rows]
            category_queryref_list = result_queryref[cols]

        tooltip_queryref_list = result_queryref.get(tooltips_data)

        projections_dict["Y"] = y_queryref_list
        projections_dict["Category"] = category_queryref_list
        if tooltip_queryref_list: projections_dict["Tooltips"] = tooltip_queryref_list

        from_list = get_from_list(table_list)

        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)
        objects_data = get_barchart_objects_data(color_field, panes, style, visual_type, rows, cols, table_column_data, datasource_column_list)

        barchart_json = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "visual_type" : bar_visual_type,
            "projections_data" : json.dumps(projections_dict),
            "from_list" : json.dumps(from_list),
            "select_list" : json.dumps(select_list),
            "objects_data" : json.dumps(objects_data),
            "title_list" : json.dumps(title_list),
            "background_list" : json.dumps(background_list),
            "border_list" : get_border_config()
        }
        overall_barchart_result.append({"config": barchart_json, "template": bar_chart_template})
        return overall_barchart_result

    except Exception as e:
        logger.error(f"---Error in generating bar chart visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating bar chart visual for {worksheet_name} - {str(e)}")

def process_bar_or_column_chart(request: VisualRequest):
    """
    Process bar or column chart visual request.
    
    Parameters
    ----------
    request : VisualRequest
        The request object containing details for the bar or column chart.
    
    Returns
    -------
    dict
        A dict containing the processed bar or column chart data.
    """
    bar_or_column_chart = {}
    rows = request.rows
    cols = request.cols
    visual_type = request.visual_type
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data
    panes = request.panes

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )

    unique_color_data = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])
    
    bar_or_column_chart_filed_mapping = {
        PowerBIReportKeys.Y.value: rows if visual_type == ChartType.STACKED_HORIZONTAL_BAR.value else cols,
        PowerBIReportKeys.CATEGORY.value: cols if visual_type == ChartType.STACKED_VERTICAL_BAR.value else rows,
    }
    if unique_color_data:
        bar_or_column_chart_filed_mapping[PowerBIReportKeys.SERIES.value] = unique_color_data

    projections_data = generate_projections_data(table_column_data, calculations_related_data, bar_or_column_chart_filed_mapping)

    bar_or_column_chart[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.COLUMN_CHART.value if visual_type == ChartType.HORIZONTAL_BAR.value else PowerBIChartTypes.BAR_CHART.value
    bar_or_column_chart[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    bar_or_column_chart[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data


    return bar_or_column_chart