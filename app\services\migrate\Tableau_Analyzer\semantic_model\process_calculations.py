from typing import <PERSON><PERSON>, List, Dict
from .datasources_handler import parse_xml
from app.core.enums import Datasource, <PERSON><PERSON><PERSON>s
from app.core.gpt_model import gpt_model
from app.core.config import logger

import xml.etree.ElementTree as ET
import json

def chunk_list(data: List[str], size: int):
    """Yield successive chunks from a list."""
    for i in range(0, len(data), size):
        yield data[i:i + size]

def normalize_formula(s: str) -> str:
    return "".join(s.split()) if s else ""

async def process_parameters(datasource):
    parameters = []

    for column in datasource.findall(Datasource.COLUMN.value):
        param_data = column.attrib.copy()

        calc_tag = column.find("calculation")
        if calc_tag is not None:
            formula = calc_tag.attrib.get("formula", "").strip()
            param_data["formula"] = formula

        parameters.append(param_data)

    return parameters

async def process_calculations(column: ET.Element) -> <PERSON><PERSON>[dict, bool]:
    """
    Process a single <column> tag.
    Returns (column_data, has_calculation)
    """
    col_data = column.attrib.copy()
    calculation_tag = column.find(Datasource.CALC.value)

    if calculation_tag is not None:
        formula = calculation_tag.attrib.get(Datasource.FORMULA.value)
        resolved_formula = ""
        #TODO: nested calculations and paramters ->
        # Take calculations.json list from analysis from s3 as input object to this method
        #
        # find resolved formula from output json
        if formula:
            col_data["formula"] = resolved_formula
        return col_data, True
    else:
        return col_data, False

async def process_datasource(datasource: ET.Element) -> Tuple[List[dict], Dict[str, List[dict]]]:

    """
    Processes a single datasource (except 'Parameters').
    Returns (columns, calculations)
    """
    calculations = {
        "measure": [],
        "dimension": []
    }

    # TODO: take analysis zip and extract it to get calculations.json and pass it to process_calculations()
    # if file not found or zip folder not there throw error 
    # and update API logic to take twb file also from s3 instead of input.

    for column in datasource.findall(Datasource.COLUMN.value):
        col_data, has_calculation = await process_calculations(column)
        if has_calculation:
            role = col_data.get(GeneralKeys.ROLE.value, "").lower()
            if role == GeneralKeys.MEASURE.value:
                calculations["measure"].append(col_data)
            else:
                calculations["dimension"].append(col_data)

    return calculations

async def simplify_table_column_json(table_column_data):
    result = []
    for datasource, tables in table_column_data.items():
        ds_obj = {}
        for table_name, table_data in tables.items():
            column_names = [col["column_name"] for col in table_data.get("column_data", [])]
            ds_obj.setdefault(datasource, {})[table_name] = column_names
        result.append(ds_obj)
    return result

async def extract_formulas(data):
    return {
        "measures": [
            measure["formula"]
            for item in data
            for _, content in item.items()
            for measure in content.get("measure", [])
            if "formula" in measure
        ],
        "dimensions": [
            dimension["formula"]
            for item in data
            for _, content in item.items()
            for dimension in content.get("dimension", [])
            if "formula" in dimension
        ]
    }

async def get_calculations(twb_file_path: str) -> dict:
    datasources_result = []
    parameters = []
    has_calc = False

    root = await parse_xml(twb_file_path)
    datasources = root.find(Datasource.DATASOURCES.value).findall(Datasource.DS.value)

    for datasource in datasources:
        datasource_name = datasource.attrib.get(Datasource.CAPTION.value, "")

        if datasource.attrib.get(Datasource.NAME.value, "") == GeneralKeys.PARAMETERS.value:
            parameters = await process_parameters(datasource)
            continue

        calculations = await process_datasource(datasource)

        if calculations["measure"] or calculations["dimension"]:
            has_calc = True

        result = {
            datasource_name: calculations
        }
        datasources_result.append(result)

    return {
        "datasources": datasources_result,
        "parameters": parameters,
        "has_calc": has_calc
    }

async def call_openai_in_chunks(formulas_data, table_info, chunk_size=10):
    all_responses = {
        "measures": [],
        "dimensions": []
    }

    async def process_chunks(formula_list, role):
        results = []
        for chunk in chunk_list(formula_list, chunk_size):
            try:
                response_str = await get_openai_pbi_response(tab_formulas=chunk, table_info=table_info)
                response_json = json.loads(response_str)
                results.extend(response_json.get("json_output", []))
            except Exception as e:
                logger.info(f"Error processing chunk for {role}: {e}")
        return results

    all_responses["measures"] = await process_chunks(formulas_data.get("measures", []), "measure")
    all_responses["dimensions"] = await process_chunks(formulas_data.get("dimensions", []), "dimension")

    return all_responses

async def inject_dax_into_calc_data(calc_data, dax_result):
    measure_map = {
        normalize_formula(item["tableau_formula"]): item["pbi_dax"]
        for item in dax_result.get("measures", [])
    }
    dimension_map = {
        normalize_formula(item["tableau_formula"]): item["pbi_dax"]
        for item in dax_result.get("dimensions", [])
    }

    for datasource in calc_data.get("datasources", []):
        for _, calculations in datasource.items():
            # Inject DAX into measures
            for measure in calculations.get("measure", []):
                key = normalize_formula(measure.get("formula"))
                if key in measure_map:
                    measure["dax_formula"] = measure_map[key]

            # Inject DAX into dimensions
            for dimension in calculations.get("dimension", []):
                key = normalize_formula(dimension.get("formula"))
                if key in dimension_map:
                    dimension["dax_formula"] = dimension_map[key]

    return calc_data

async def get_openai_pbi_response(tab_formulas, table_info):
    model="gpt-4o"
    response_format = "json_object"
    user_message = f"Tableau Formula:{tab_formulas}, Table Information:{table_info}"

    system_message = '''You are an expert in both Tableau and Power BI. Your task is to convert a list of Tableau calculated fields into their corresponding Power BI DAX functions. For each Tableau formula, you must accurately translate it into the equivalent DAX expression in Power BI, ensuring that the table and column names are maintained exactly as provided.

            Input Format:
            Tableau Formula: A list of Tableau calculated fields (formulae) that need to be converted.

            Table Information: A dictionary containing the table names and their associated columns.

            Output Instructions:
            DAX Conversion: Provide the exact Power BI DAX equivalent for each Tableau formula.

            Preserve Structure: The structure of the Tableau formula must be maintained without alteration in the DAX conversion.

            No Additional Aggregations: Do not introduce any new aggregations unless explicitly specified in the Tableau formula.

            Exact Field Mapping: Ensure the translated DAX formula references the correct table and field names from the provided table information.

            Formatting: Output the results in the following JSON format, ensuring the Power BI DAX formula is in a single line:

            {
            "json_output": [
                {
                "tableau_formula": "<Tableau formula>",
                "pbi_dax": "<Power BI DAX formula>"
                }
            ]
            }

            Make sure all formulas are carefully translated to maintain functional equivalence between Tableau and Power BI without skipping any.'''


    return gpt_model(model, system_message, user_message, response_format)