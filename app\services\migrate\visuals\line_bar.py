from ..core import *
from app.core import line_bar_json
import json, uuid
from app.core.enums import *
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    generate_projections_data,
    extract_multiple_encodings_data,
)
def get_line_bar_chart_report(rows, cols,table_column_data, datasource_col_list,orderby,worksheet_name):
    try:
        linebar_result=[]
        from_list,select_list,cat_ref_list,y_ref_list,y2_ref_list,orderby_list,select_list_no_diplcates,tables_names,columnproperties_json=[],[],[],[],[],[],[],[],{}
        rowslist=get_fields_data(rows)
        row1,row2=[rowslist[0]],rowslist[1:]
        for rows in row1:
            rows=rows.strip()
            y_ref_list,y_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,y_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            columnproperties_json.update(get_column_properties_json(*list(get_calc_filter_column(rows, table_column_data,datasource_col_list))))
            if y_tab_name not in tables_names:tables_names.append(y_tab_name)

        for rows in row2:
            rows=rows.strip()
            y2_ref_list,y2_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,y2_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            columnproperties_json.update(get_column_properties_json(*list(get_calc_filter_column(rows, table_column_data,datasource_col_list))))
            if y2_tab_name not in tables_names:tables_names.append(y2_tab_name)

        cols_list=get_fields_data(cols)
        for rows in cols_list:
            cat_ref_list,cat_tab_name=query_ref_list(rows,table_column_data,datasource_col_list,cat_ref_list)
            select_list=get_select_json_list(*list(get_calc_filter_column(rows,table_column_data,datasource_col_list)),select_list)
            if cat_tab_name not in tables_names:tables_names.append(cat_tab_name)

        orderby=[orderby] if isinstance(orderby,dict) else orderby
        for eachorder in orderby:
            if eachorder.get(TableauXMLTags.USING.value):
                direction_order=1 if eachorder.get(TableauXMLTags.DIRECTION.value)=="ASC" else 2
                order_list=get_fields_data(eachorder.get(TableauXMLTags.USING.value))
                for category in order_list:
                    orderfil,ordercol,ordertab=get_calc_filter_column(category,table_column_data,datasource_col_list)
                    orderby_list.extend(get_order_by_list(category,table_column_data,datasource_col_list,direction_order))
                    select_list=get_select_json_list(*list(get_calc_filter_column(eachorder,table_column_data,datasource_col_list)),select_list)
                    if ordertab not in tables_names:tables_names.append(ordertab)

        from_list=get_from_list(tables_names)
        for i in select_list:
            if i not in select_list_no_diplcates:select_list_no_diplcates.append(i)

        linebar_json = {
            "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
            "cat_ref_list" : json.dumps(cat_ref_list),
            "y1_ref_list" : json.dumps(y_ref_list),
            "y2_ref_list" : json.dumps(y2_ref_list),
            "from_list" : json.dumps(from_list),
            "select_list" :json.dumps(select_list_no_diplcates),
            "columnproperties_json":json.dumps(columnproperties_json)
        }
        linebar_result.append({"config":linebar_json,"template":line_bar_json})
        return linebar_result

    except Exception as e:
        logger.error(f"---Error in generating line bar visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating line bar visual for {worksheet_name} - {str(e)}")

def get_secondary_axis(style_data):
    '''retrieves the value of 'field' from 'encoding' tag in styles for secondary axis'''

    return (
        style_data.get(TableauXMLTags.AXIS.value, {})
            .get(TableauXMLTags.ENCODING.value, {})
            .get(TableauXMLTags.FIELD.value)
    )

def process_line_bar_chart_report(request: VisualRequest):
    """
    Process line bar chart visual request.

    Parameters
    ----------
    request : VisualRequest
        The request object containing details for the line bar chart.

    Returns
    -------
    list
        A list containing the processed line bar chart data.
    """
    line_bar_chart_result = {}
    panes = request.panes
    rows = request.rows
    cols = request.cols
    table_column_data = request.table_column_data
    style_data = request.worksheet_style_data
    calculations_related_data = request.calculations_related_data
    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )

    unique_tooltips = remove_duplicate_fields(
        encodings[TableauXMLTags.TOOLTIP.value],
        encodings[TableauXMLTags.LOD.value],
        encodings[TableauXMLTags.TEXT.value],
        rows,
        cols
    )
    unique_color_data = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])
    secondary_axis = get_secondary_axis(style_data)

    line_bar_chart_filed_mapping = {
        PowerBIReportKeys.Y.value: rows,
        PowerBIReportKeys.CATEGORY.value: cols,
    }

    if unique_tooltips:
        line_bar_chart_filed_mapping[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips
    if not secondary_axis and unique_color_data:
        line_bar_chart_filed_mapping[PowerBIReportKeys.SERIES.value] = unique_color_data
    if secondary_axis:
        line_bar_chart_filed_mapping[PowerBIReportKeys.Y2.value] = [secondary_axis]

    projections_data = generate_projections_data(table_column_data, calculations_related_data, line_bar_chart_filed_mapping)

    line_bar_chart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.LINE_STACKED_COLUMN_COMBO_CHART.value
    line_bar_chart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    line_bar_chart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data

    return line_bar_chart_result

"""
This can be handeld in line stacked chart and line chart
"""