import uuid
from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, cast, text, or_, case
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, Query, joinedload
from app.core.session import Base,scoped_context
from app.models.base import AuditMixin
from app.models.users import User, Role
from app.core.session import scoped_context, Base
from app.core.enums import RoleEnum
from sqlalchemy.orm import joinedload


class ProjectDetail(Base, AuditMixin):
    __tablename__ = "project_details"
    __table_args__ = {"schema": "biport_dev"}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    is_upload = Column(Boolean, server_default=text("false"), nullable=False)

    site_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_site_details.id"), nullable=True)
    server_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.tableau_server_details.id"), nullable=True)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.project_details.id"), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=False)
    assigned_to = Column(UUID(as_uuid=True), ForeignKey("biport_dev.users.id"), nullable=True)

    creator = relationship("User", foreign_keys=[user_id], back_populates="created_projects")
    assignee = relationship("User", foreign_keys=[assigned_to], back_populates="assigned_projects")
    reports = relationship("ReportDetail", back_populates="project")

    @staticmethod
    def get_projects_by_user_role(session, user, role_name: str) -> Query:
        """
        Return a SQLAlchemy query for projects based on user's role and organization.
        """
        query = session.query(ProjectDetail).options(joinedload(ProjectDetail.reports))

        # Filter by organization
        query = query.join(ProjectDetail.creator).filter(User.organization_id == user.organization_id)

        #Role-based access control
        if role_name == "Admin":
            pass  # See all
        elif role_name == "Manager":
            subordinate_ids = session.query(User.id).filter(User.manager_id == user.id).all()
            subordinate_ids = [sid[0] for sid in subordinate_ids]
            query = query.filter(
                or_(
                    ProjectDetail.assigned_to == user.id,
                    ProjectDetail.assigned_to.in_(subordinate_ids)
                )
            )
        elif role_name == "Developer":
            query = query.filter(ProjectDetail.assigned_to == user.id)

        return query
        


class ProjectDetailManager:
    @staticmethod
    def get_all_root_projects(page: int, page_size: int):
        from app.models.report_details import ReportDetail
        offset = (page - 1) * page_size
        with scoped_context() as session:
            return (
                session.query(ProjectDetail)
                .filter(
                    ProjectDetail.is_upload == True,
                )
                .order_by(ProjectDetail.updated_at.desc())
                .offset(offset)
                .limit(page_size)
                .all()
            )
        
    @staticmethod
    def assign_user_to_project(project_id: UUID, user_id: UUID):
        with scoped_context() as session:
            project = session.query(ProjectDetail).options(
                joinedload(ProjectDetail.assignee)
            ).filter(
                ProjectDetail.id == project_id,
                ProjectDetail.is_deleted == False
            ).first()

            if not project:
                return None

            project.assigned_to = user_id
            session.commit()
            session.refresh(project)
            return project      
        
    @staticmethod
    def get_projects_by_site(site_id: UUID):
        from app.models.report_details import ReportDetail
        with scoped_context() as session:
            admin_user = (
                session.query(User)
                .filter(User.role.has(name=RoleEnum.ADMIN.value), User.is_deleted == False)
                .first()
            )
            admin_id = admin_user.id if admin_user else None

            admin_user = (
                session.query(User)
                .filter(User.role.has(name=RoleEnum.ADMIN.value), User.is_deleted == False)
                .first()
            )
            admin_id = admin_user.id if admin_user else None

            root_projects = (
                session.query(ProjectDetail)
                .filter(
                    ProjectDetail.site_id == site_id,
                    ProjectDetail.parent_id == None,
                    ProjectDetail.is_deleted == False
                )
                .all()
            )

            project_ids = [p.id for p in root_projects]

            sub_projects = (
                session.query(ProjectDetail)
                .filter(
                    ProjectDetail.parent_id.in_(project_ids),
                    ProjectDetail.is_deleted == False
                )
                .all()
            )

            for sp in sub_projects:
                if not sp.assigned_to:
                    sp.assigned_to = admin_id

            assigned_user_ids = set()
            for sp in sub_projects:
                if not sp.assigned_to:
                    sp.assigned_to = admin_id
                if sp.assigned_to:
                    assigned_user_ids.add(sp.assigned_to)

            reports = (
                session.query(ReportDetail)
                .filter(
                    ReportDetail.project_id.in_([sp.id for sp in sub_projects]),
                    ReportDetail.is_deleted == False
                )
                .all()
            )

            # Fetch all assigned users in one query and map by id
            assigned_users = (
                session.query(User)
                .filter(User.id.in_(assigned_user_ids), User.is_deleted == False)
                .all()
            )
            assigned_users_map = {user.id: user for user in assigned_users}

            return root_projects, sub_projects, reports, assigned_users_map

    @staticmethod
    def get_projects_by_parent(parent_id: UUID):
        from app.models.report_details import ReportDetail
        with scoped_context() as session:
            admin_user = session.query(User).join(User.role).filter(Role.name == RoleEnum.ADMIN).first()
            admin_id = admin_user.id if admin_user else None

            sub_projects = session.query(
                ProjectDetail.id,
                ProjectDetail.name,
                ProjectDetail.parent_id,
                case(
                    (ProjectDetail.assigned_to == None, admin_id),
                    else_=ProjectDetail.assigned_to
                ).label("assigned_to"),
                case(
                    (ProjectDetail.assigned_to == None, admin_user.name if admin_user else None),
                    else_=User.name
                ).label("assigned_to_name"),
                User.role_id.label("role_id"),
                User.manager_id.label("manager_id")
            ).outerjoin(User, User.id == ProjectDetail.assigned_to).filter(
                ProjectDetail.parent_id == parent_id,
                ProjectDetail.is_deleted == False
            ).all()

            sub_project_ids = [sp.id for sp in sub_projects]

            reports = []
            if sub_project_ids:
                reports = session.query(
                    ReportDetail.id,
                    ReportDetail.name,
                    ReportDetail.project_id,
                    ReportDetail.view_count
                ).filter(
                    ReportDetail.project_id.in_(sub_project_ids),
                    ReportDetail.is_deleted == False
                ).all()

            return sub_projects, reports


    @staticmethod
    def soft_delete_by_server_id(server_id):
        from app.models.project_details import ProjectDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            session.query(ProjectDetail).filter_by(server_id=server_id).update({"is_deleted": True})
            session.commit()

    @staticmethod
    def get_ids_by_server_id(server_id):
        from app.models.project_details import ProjectDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            return [p.id for p in session.query(ProjectDetail).filter_by(server_id=server_id).all()]

    @staticmethod
    def get_root_projects_by_user_id(user_id: uuid.UUID, offset: int = 0, limit: int = 10):
        with scoped_context() as session:
            return session.query(ProjectDetail).filter(
                ProjectDetail.user_id == user_id,
                ProjectDetail.parent_id == None,
                ProjectDetail.is_deleted == False
            ).order_by(ProjectDetail.updated_at.desc()).offset(offset).limit(limit).all()

    @staticmethod
    def get_total_root_projects_by_user_id(user_id: uuid.UUID) -> int:
        with scoped_context() as session:
            return session.query(ProjectDetail).filter(
                ProjectDetail.user_id == user_id,
                ProjectDetail.parent_id == None,
                ProjectDetail.is_deleted == False
            ).count()

    @staticmethod
    def get_projects_by_parent_and_user_id(parent_id: uuid.UUID, user_id: uuid.UUID, offset: int = 0, limit: int = 10):
        with scoped_context() as session:
            return session.query(ProjectDetail).filter(
                ProjectDetail.user_id == user_id,
                ProjectDetail.parent_id == parent_id,
                ProjectDetail.is_deleted == False
            ).order_by(ProjectDetail.updated_at.desc()).offset(offset).limit(limit).all()

    @staticmethod
    def get_total_projects_by_parent_and_user_id(parent_id: uuid.UUID, user_id: uuid.UUID) -> int:
        with scoped_context() as session:
            return session.query(ProjectDetail).filter(
                ProjectDetail.user_id == user_id,
                ProjectDetail.parent_id == parent_id,
                ProjectDetail.is_deleted == False
            ).count()

    @staticmethod
    def is_duplicate_project(name: str, user_id: uuid.UUID, parent_id: uuid.UUID = None) -> bool:
        with scoped_context() as session:
            query = session.query(ProjectDetail).filter(
                ProjectDetail.name == name,
                ProjectDetail.user_id == user_id,
                ProjectDetail.is_deleted == False
            )
            if parent_id is None:
                query = query.filter(ProjectDetail.parent_id == None)
            else:
                query = query.filter(ProjectDetail.parent_id == parent_id)
            return session.query(query.exists()).scalar()

    @staticmethod
    def is_duplicate_project_exclude_id(name: str, user_id: uuid.UUID, parent_id: uuid.UUID = None, exclude_id: uuid.UUID = None) -> bool:
        with scoped_context() as session:
            query = session.query(ProjectDetail).filter(
                ProjectDetail.name == name,
                ProjectDetail.user_id == user_id,
                ProjectDetail.is_deleted == False
            )
            if parent_id is None:
                query = query.filter(ProjectDetail.parent_id == None)
            else:
                query = query.filter(ProjectDetail.parent_id == parent_id)
            if exclude_id is not None:
                query = query.filter(ProjectDetail.id != exclude_id)
            return session.query(query.exists()).scalar()

    @staticmethod
    def update_project_name(project_id: uuid.UUID, new_name: str, updated_by: uuid.UUID):
        with scoped_context() as session:
            project = session.query(ProjectDetail).filter(ProjectDetail.id == project_id).first()
            if not project:
                return None
            project.name = new_name
            project.updated_by = updated_by
            session.commit()
            session.refresh(project)
            return project

    @staticmethod
    def get_project_by_id(project_id: uuid.UUID):
        with scoped_context() as session:
            return session.query(ProjectDetail).filter(ProjectDetail.id == project_id).first()

    @staticmethod
    def add_folder(id, name, user_id, parent_id=None, created_by=None, updated_by=None, is_upload=False, session=None):
        from app.models.project_details import ProjectDetail
        from app.core.session import scoped_context
        if session is None:
            with scoped_context() as session:
                project = ProjectDetail(
                    id=id,
                    name=name,
                    site_id=None,
                    server_id=None,
                    user_id=user_id,
                    parent_id=parent_id if parent_id is not None else None,
                    created_by=created_by,
                    updated_by=updated_by,
                    is_upload=is_upload
                )
                session.add(project)
                session.commit()
                session.refresh(project)
                return project
        else:
            project = ProjectDetail(
                id=id,
                name=name,
                site_id=None,
                server_id=None,
                user_id=user_id,
                parent_id=parent_id if parent_id is not None else None,
                created_by=created_by,
                updated_by=updated_by,
                is_upload=is_upload
            )
            session.add(project)
            session.flush()
            session.refresh(project)
            return project

    @staticmethod
    def soft_delete_project_and_children(project_id):
        from app.models.project_details import ProjectDetail
        from app.models.report_details import ReportDetail
        from app.core.session import scoped_context
        with scoped_context() as session:
            # Recursively find all child projects
            def get_all_descendant_project_ids(pid):
                ids = [pid]
                children = session.query(ProjectDetail.id).filter(ProjectDetail.parent_id == pid, ProjectDetail.is_deleted == False).all()
                for (child_id,) in children:
                    ids.extend(get_all_descendant_project_ids(child_id))
                return ids
            all_project_ids = get_all_descendant_project_ids(project_id)
            # Soft delete all projects
            session.query(ProjectDetail).filter(ProjectDetail.id.in_(all_project_ids)).update({"is_deleted": True}, synchronize_session=False)
            # Soft delete all reports under these projects
            session.query(ReportDetail).filter(ReportDetail.project_id.in_(all_project_ids)).update({"is_deleted": True}, synchronize_session=False)
            session.commit()

    @staticmethod
    async def process_zip_upload(extract_dir, project_id, filename, user, org_name, s3, ReportDetailManager):
        import os
        from fastapi import HTTPException
        import uuid as uuidlib
        from app.core.session import scoped_context
        with scoped_context() as session:
            async def process_folder(abs_path, parent_project_id, rel_path):
                folder_name = os.path.basename(abs_path)
                if rel_path == '':
                    project_name = os.path.splitext(filename)[0]
                else:
                    project_name = folder_name
                if ProjectDetailManager.is_duplicate_project(project_name, user.id, parent_id=parent_project_id):
                    raise HTTPException(status_code=409, detail=f"Project '{project_name}' already exists.")
                project = ProjectDetailManager.add_folder(
                    id=uuidlib.uuid4(),
                    name=project_name,
                    user_id=user.id,
                    parent_id=parent_project_id,
                    created_by=user.id,
                    updated_by=user.id,
                    is_upload=True,
                    session=session
                )
                for entry in os.listdir(abs_path):
                    entry_path = os.path.join(abs_path, entry)
                    entry_rel_path = os.path.join(rel_path, entry) if rel_path else entry
                    if os.path.isdir(entry_path):
                        await process_folder(entry_path, project.id, entry_rel_path)
                    else:
                        if not (entry.endswith('.twb') or entry.endswith('.twbx')):
                            raise HTTPException(status_code=400, detail=f"Invalid file '{entry_rel_path}': only .twb/.twbx files allowed.")
                        if ReportDetailManager.is_duplicate_report(entry, project.id, user.id):
                            continue
                        report = ReportDetailManager.add_report(
                            id=uuidlib.uuid4(),
                            name=entry,
                            report_id=str(uuidlib.uuid4()),
                            project_id=project.id,
                            created_by=user.id,
                            updated_by=user.id,
                            session=session
                        )
                        s3_path = f"BI-PortV3/{org_name}/{report.report_id}/tableau_file/{entry}"
                        await s3.upload_to_s3(file_path=entry_path, object_name=s3_path)
                return project
            await process_folder(extract_dir, project_id, '')
            session.commit()
