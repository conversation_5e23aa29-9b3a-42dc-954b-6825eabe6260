from app.services.migrate import process_tableau_datasources
from app.core.enums import PowerBITemplateKeys
import uuid

async def process_column_data(data_type, aggregation):
    if data_type == "integer":
        data_type = "int64"
        format_string = 0
    elif data_type in ("double", "real"):
        data_type = "decimal"
        format_string = "0.00"
    elif data_type in ("date", "datetime"):
        data_type = "datetime"
        format_string = "General Date"
        aggregation = "none"
    elif data_type == "string":
        format_string = "none"
        aggregation = "none"
    else:
        format_string = "none"
        aggregation = "none"
    return {
        'data_type': data_type,
        'format_string': format_string,
        'aggregation': aggregation.lower()
    }

async def generate_variation_data(column_name, table_name, date_columns_data):
    date_column_info = None
    variation = None
    local_date_table_ref_key = PowerBITemplateKeys.LOCAL_DATE_TABLE_REF.value
    relationship_id_key = PowerBITemplateKeys.RELATIONSHIP_ID.value
    for column_info in date_columns_data:
        if column_info.get(PowerBITemplateKeys.COLUMN_NAME.value) == column_name and column_info.get("table_name") == table_name:
            date_column_info = column_info
            break
    if date_column_info:
        local_date_table_ref = date_column_info.get(local_date_table_ref_key)
        relationship_id = date_column_info.get(relationship_id_key)
        variation = {
            local_date_table_ref_key: local_date_table_ref,
            relationship_id_key: relationship_id,
        }
    return variation

async def process_date_columns(table_column_data):
    date_columns_data = []
    for datasource_name, tables_data in table_column_data.items():
        for table_name, table_info in tables_data.items():
            columns_info = table_info.get(PowerBITemplateKeys.COLUMN_DATA.value, [])
            for column in columns_info:
                data_type = column.get(PowerBITemplateKeys.DATA_TYPE.value)
                column_name = column.get(PowerBITemplateKeys.COLUMN_NAME.value)
                if data_type in ("date", "datetime"):
                    unique_id = str(uuid.uuid4())
                    local_date_table_ref = f"LocalDateTable_{unique_id}"
                    date_columns_data.append({
                        "column_name": column_name,
                        "local_date_table_ref": local_date_table_ref,
                        "table_name": table_name,
                        "relationship_id": str(uuid.uuid4())
                    })
    return date_columns_data

async def process_table_columns(twb_file_path):
    
    table_columns = {}
    table_column_data, _ = await process_tableau_datasources(twb_file_path)
    unique_id = PowerBITemplateKeys.UNIQUE_ID.value
    column_data_key = PowerBITemplateKeys.COLUMN_DATA.value
    aggregation_key = PowerBITemplateKeys.AGGREGATION.value
    data_type_key = PowerBITemplateKeys.DATA_TYPE.value
    column_name_key = PowerBITemplateKeys.COLUMN_NAME.value
    format_string_key = PowerBITemplateKeys.FORMAT_STRING.value
    summarize_by_key = PowerBITemplateKeys.SUMMARIZE_BY.value
    source_column_key = PowerBITemplateKeys.SOURCE_COLUMN.value
    lineage_tag_key = PowerBITemplateKeys.LINEAGE_TAG.value
    variation_key = PowerBITemplateKeys.VARIATION.value
    date_columns = await process_date_columns(table_column_data)
    for datasource_name, tables_data in table_column_data.items():
        table_columns.setdefault(datasource_name, {})
        for table_name, table_info in tables_data.items():
            table_columns[datasource_name].setdefault(table_name, {
                lineage_tag_key: table_info.get(unique_id),
                "connection_type": table_info.get("connection_type"),
                "file_path": table_info.get("directory"),
                "server": table_info.get("server"),
                "database": table_info.get("database"),
                column_data_key: []
            })
            columns_info = table_info.get(column_data_key, [])
            for column in columns_info:
                variation = None
                data_type = column.get(data_type_key)
                aggregation = column.get(aggregation_key, 'none')
                column_name = column.get(column_name_key)
                if data_type in ("date", "datetime"):
                    variation = await generate_variation_data(column_name, table_name, date_columns)
                processed_data = await process_column_data(data_type, aggregation)
                column_data = {
                    column_name_key: f"'{column_name}'" if " " in column_name else column_name,
                    lineage_tag_key: column.get(unique_id),
                    data_type_key: processed_data.get(data_type_key),
                    format_string_key: processed_data.get(format_string_key),
                    summarize_by_key: processed_data.get(aggregation_key),
                    source_column_key : column_name,
                    variation_key: variation
                }
                table_columns[datasource_name][table_name][column_data_key].append(column_data)
    return table_columns, date_columns