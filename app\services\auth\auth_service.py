from datetime import datetime
import json
import re

from app.models.roles import RoleManager
from app.models.users import UserManager
from app.core import (
    ServiceResponse, AuthenticationError,
    AuthorizationError, BaseService
)
from app.core.exceptions import ConflictError, ValidationError
from app.core.config import ACCESS_TOKEN_EXPIRY, REFRESH_TOKEN_EXPIRY, logger
from app.schemas.auth import UserCreate, UserLogin, ResetPassword, RefreshToken, AddUserRequest
from .jwt_token import create_secure_jwt, encode_base, decode_base, decode_secure_jwt
from .token_blacklist import blacklist_token
from app.models.roles import RoleManager
from app.models.users import User<PERSON>anager
from uuid import UUID
from app.models.project_details import ProjectDetailManager
class AuthService(BaseService):
    """Service class to handle user authentication lifecycle."""

    def register_user(self, data: UserCreate):
        user_manager = UserManager(
            name=data.name,
            email=data.email,
            organization_name=data.organizationName,
            phone_number=data.phoneNumber,
            designation=data.designation,
            reports_number=data.numberOfReports,
            password=data.password
        )
        user_manager.add_user()
        logger.info(f"User registered: {data.email}")
        return ServiceResponse.success("User added successfully")

    def login_user(self, data: UserLogin):
        user = UserManager.login_user(data.email, data.password)
        role_name = RoleManager.get_role_name(user.role_id)

        access_token = create_secure_jwt({
            "organization_id": str(user.organization_id),
            "email": user.email,
            "name": user.name,
            "role": role_name
        }, ACCESS_TOKEN_EXPIRY)
        refresh_token = create_secure_jwt({"email": user.email}, REFRESH_TOKEN_EXPIRY)

        logger.info(f"User logged in: {data.email}")
        return ServiceResponse.success({
            "message": "User verified successfully",
            "access_token": access_token,
            "refresh_token": refresh_token,
            "organization_id": str(user.organization_id),
            "role": role_name,
            "user_id": str(user.id),
            "user_name": user.name,
            "user_email": encode_base(user.email),
        })
    def logout_user(self, payload: tuple[str, str, str]):

        email_encoded, access_token, refresh_token = payload
        email = decode_base(email_encoded)

        user = UserManager.get_user_by_email(email)
        if not user:
            logger.warning(f"Logout attempt for unknown email: {email}")
            raise AuthenticationError("User not found")

        self._validate_token_and_blacklist(access_token, email, token_type="access")
        self._validate_token_and_blacklist(refresh_token, email, token_type="refresh")

        logger.info(f"User logged out: {email}")
        return ServiceResponse.success({"message": "User logged out successfully"})

    def refresh_token(self, data: RefreshToken):
        email = decode_base(data.email)

        payload = decode_secure_jwt(data.refresh_token, email)
        exp_timestamp = payload.get("exp")
        if exp_timestamp and exp_timestamp < datetime.utcnow().timestamp():
            raise AuthorizationError("Refresh token has expired")

        user = UserManager.get_user_by_email(email)
        role_name = RoleManager.get_role_name(user.role_id)
        if not user:
            raise AuthenticationError("User not found")

        new_access_token = create_secure_jwt({
            "organization_id": str(user.organization_id),
            "email": email,
            "name": user.name,
            "role": role_name
        }, ACCESS_TOKEN_EXPIRY)
        new_refresh_token = create_secure_jwt({"email": email}, REFRESH_TOKEN_EXPIRY)

        return ServiceResponse.success({
            "message": "Token refreshed successfully",
            "access_token": new_access_token,
            "refresh_token": new_refresh_token,
            "organization_id": str(user.organization_id),
            "role": role_name,
            "user_id": str(user.id),
            "user_name": user.name,
            "user_email": encode_base(email),
        })

    def reset_password(self, data: ResetPassword, email: str):
        UserManager.update_user_password(email, data.password)
        logger.info(f"Password reset for email: {email}")
        return ServiceResponse.success({"message": "Password reset successfully"})

    def _validate_token_and_blacklist(self, token: str, email: str, token_type: str):
        payload = decode_secure_jwt(token, email)
        sub = json.loads(payload.get("sub"))
        token_email = sub.get("email")

        if token_email != email:
            raise AuthenticationError(f"{token_type.capitalize()} token email mismatch")

        exp = payload.get("exp")
        if exp:
            ttl = int(exp - datetime.utcnow().timestamp())
            if ttl > 0:
                blacklist_token(token, ttl)

    
    
    def _get_projects_by_parent_id(self, parent_id:UUID) -> ServiceResponse:
        sub_projects, reports = ProjectDetailManager.get_projects_by_parent(parent_id)

        report_map = {}
        for report in reports:
            report_map.setdefault(report.project_id, []).append({
                "id": str(report.id),
                "file_name": str(report.name),
                "project_id": str(report.project_id),
                "view_count": report.view_count
            })

        result = []
        for sp in sub_projects:
            result.append({
                "id": str(sp.id),
                "name": sp.name,
                "parent_id": str(sp.parent_id),
                "files": report_map.get(sp.id, []),
                "assigned_to": str(sp.assigned_to),
                "assigned_to_name": sp.assigned_to_name 
            })

        return ServiceResponse.success(data={"sub-projects": result})

    def add_user_details(self, data: AddUserRequest, user_email: str):
        from app.core.dependencies import check_if_admin
        check_if_admin(user_email)

        user_manager = UserManager(
            name=data.name,
            email=data.email,
            password=data.password,
            phone_number=data.phone_number,
            organization_id=data.organization_id,
            role_id=data.role_id,
            manager_id=data.manager_id
        )
        user_manager.add_user_details()

        return ServiceResponse.success({
            "message": "User added successfully",
            "user_name": data.name,
            "user_email": data.email
        })


    def get_all_roles(self):
        roles = RoleManager.get_role_info()
        if not roles:
            raise AuthenticationError("No roles found")

        response = [
            {
                "role_id": str(role.id),
                "role_name": role.name.value
            }
            for role in roles
        ]
        return ServiceResponse.success(response)
    
    def get_organization_managers(self, org_id: str):
        managers = UserManager.get_managers_by_organization(org_id)

        if not managers:
            raise AuthenticationError("No managers found for the given organization.")

        response = [
            {
                "id": str(mgr.id),
                "name": mgr.name
            }
            for mgr in managers
        ]

        return ServiceResponse.success(data=response)

    def add_user_service(self, data: AddUserRequest):
        '''Add a new user to the database.'''
        if not re.match(r'^[a-zA-Z\s]+$', data.name):
            raise ValidationError("Please enter a valid name")

        # email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        # if not re.match(email_pattern, data.email):
        #     raise ValidationError("Please enter a valid email address")

        existing_user = UserManager.check_exists_email_mobile(data)
        if existing_user:
            if existing_user.email.lower() == data.email.lower():
                raise ConflictError("Email already exists")
            if existing_user.phone_number == data.phone_number:
                raise ConflictError("Phone number already exists")
        
        user = UserManager.add_user(data)
        return ServiceResponse.success({
            "message": "User added successfully",
            "user_name": user.name,
            "user_email": user.email
        })
    
    def get_organization_users(self, org_id: str):
        users = UserManager.get_users_by_organization(org_id)

        if not users:
            raise AuthenticationError("No managers or developers found for the given organization.")

        response = [
            {
                "id": str(user.id),
                "name": user.name,
                "email": user.email,
                "phone_number": user.phone_number,
                "role_id": str(user.role_id),
                "role_name": user.role_name,
                "manager_id": str(user.manager_id) if user.manager_id else None,
                "organization_id": str(user.organization_id)
            }
            for user in users
        ]

        return ServiceResponse.success(data=response)
    
    def update_user_details(self, user_id: str, user_data: dict):
        UserManager.update_user(user_id, user_data)

        response = {"message": "User updated successfully"}
        return ServiceResponse.success(data=response)
