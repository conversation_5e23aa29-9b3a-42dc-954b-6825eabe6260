import uuid
import json
from app.core.enums import  GeneralKeys, PowerBIReportKeys, PowerBITemplateKeys, FilterValues, PowerBIObjectKeys
from app.core.constants import (
    AGGREGATION_NATIVE_REFERENCE, AGGREGATION_QUERY_REF, DATE_NATIVE_REFERENCE, DATE_QUERY_REF,
    TRUNCATED_DATE_TIME, AGGREGATION_SELECTION, DATE_HEIRARCHY_SELECTION, COLUMN_SELECTION, COLUMNN_QUERY_REF,
    TABLEAU_WORKSHEET_DIMENSIONS
)


def get_config_json(visual_details):
    """
    Generates a configuration JSON for PowerBI visual.
    As Text box is already processed we return the sam ebox in return!
    """
    if GeneralKeys.TEXT_BOX_VISUAL.value in visual_details.keys(): return visual_details.get(GeneralKeys.TEXT_BOX_VISUAL.value)

    config_json = {}
    config_json[PowerBIReportKeys.NAME.value] = get_uuid()
    config_json[PowerBIReportKeys.LAYOUTS.value] = get_default_worksheet_dimentions()
    config_json[PowerBIReportKeys.SINGLE_VISUAL.value] = get_single_visual(visual_details)
    return config_json


def get_single_visual(visual_details):
    """Constructs and returns a dictionary representing a single Visual of  Power BI visual from the provided details."""

    single_visual = {}
    single_visual[PowerBIReportKeys.VISUAL_TYPE.value] = visual_details.get(PowerBITemplateKeys.VISUAL_TYPE.value)
    projections = get_projection_data(visual_details.get(PowerBITemplateKeys.PROJECTIONS_DATA.value))
    single_visual[PowerBIReportKeys.PROJECTIONS.value] = projections
    single_visual[PowerBIReportKeys.PROTOTYPE_QUERY.value] = get_prototypequery_data(visual_details.get(PowerBITemplateKeys.PROJECTIONS_DATA.value))
    single_visual[PowerBIReportKeys.DRILL_FILTER_OTHER_VISUAL.value] = True
    single_visual[PowerBIReportKeys.HAS_DEFAULT_SORT.value] = True
    objects_data = visual_details.get(PowerBITemplateKeys.OBJECTS.value)
    if objects_data:
        single_visual[PowerBIReportKeys.OBJECTS.value] = convert_objects_data_to_powerbi_format(objects_data)
    return single_visual


def form_queryref_list(qref_set):
    # Returns a list of dicts with "queryRef" and "active" keys for each value in qrefs_list
    return [{PowerBIReportKeys.QUERYREF.value: value, PowerBIReportKeys.ACTIVE.value: True} for value in qref_set]


def get_projection_data(projection_details):
    """
    Processes projection details to generate and format query references for each key.
    """
    projections = {}
    for key, details in projection_details.items():
        queryref_list = []
        for detail in details:
            column = detail.get(PowerBITemplateKeys.COLUMN.value)
            filter_value = detail.get(PowerBITemplateKeys.FILTER.value)
            table = detail.get(PowerBITemplateKeys.TABLE_NAME.value)
            qref = get_queryref_data(filter_value, column, table)
            queryref_list.extend(qref) if isinstance(qref, list) else queryref_list.extend([qref])
        projections[key] = form_queryref_list(list(dict.fromkeys(queryref_list)))
    return projections


def get_selection_list(projection_details):
    """
    Generates a selection list based on the provided projection details.
    This function flattens the input projection details, iterates through each unique projection,
    and constructs a list of selection JSON objects using the specified column, filter, and table name.
    """

    unique_projections_data = flatten_projection_details(projection_details)
    select_list = []
    for projection in unique_projections_data:
        column = projection.get(PowerBITemplateKeys.COLUMN.value)
        filter_value = projection.get(PowerBITemplateKeys.FILTER.value)
        table_name = projection.get(PowerBITemplateKeys.TABLE_NAME.value)
        select_list.extend(get_select_json(column, filter_value, table_name))
    return select_list


def get_from_list(projection_details):
    """Returns a list of dictionaries representing table names with associated metadata from projection_details."""
    
    all_table_name_list = get_all_table_name_list(projection_details)
    return [{PowerBIReportKeys.NAME_IN_FROM_LIST.value: table_name,PowerBIReportKeys.ENTITY.value: table_name,PowerBIReportKeys.TYPE.value: 0} for table_name in all_table_name_list]


def get_prototypequery_data(projections_data):
    """Generates a prototype PowerBI query dictionary from projections data."""
    
    prototype_query = {}
    prototype_query[PowerBIReportKeys.VERSION.value] = 2
    prototype_query[PowerBIReportKeys.FROM.value] = get_from_list(projections_data)
    prototype_query[PowerBIReportKeys.SELECT.value] = get_selection_list(projections_data)
    # TODO 
    # Have to handle Orderby

    return prototype_query


def get_nativeReferenceName(filter_value, column_value):
    """
    Generates a native reference name for a given column based on the filter value.
    Depending on the presence of specific substrings in the filter_value, the function
    returns a formatted string representing the native reference name for the columns
    """

    if FilterValues.YR.value in filter_value:
        return DATE_NATIVE_REFERENCE.format(column_value=column_value, date_level=FilterValues.YEAR.value)
    elif FilterValues.MN.value in filter_value:
        return DATE_NATIVE_REFERENCE.format(column_value=column_value, date_level=FilterValues.MONTH.value)
    elif FilterValues.QR.value in filter_value: 
        return DATE_NATIVE_REFERENCE.format(column_value=column_value, date_level=FilterValues.QUARTER.value)
    elif FilterValues.DY.value in filter_value:
        return DATE_NATIVE_REFERENCE.format(column_value=column_value, date_level=FilterValues.DAY.value)
    elif FilterValues.SUM.value in filter_value:
        return AGGREGATION_NATIVE_REFERENCE.format(aggregation=FilterValues.SUM.value, column_value=column_value)
    elif FilterValues.CNT.value in filter_value:
        return AGGREGATION_NATIVE_REFERENCE.format(aggregation=FilterValues.COUNT.value, column_value=column_value)
    else:
        return column_value


def flatten_projection_details(projection_details):
    """
    Flattens a dictionary of projection details into a list of all contained dictionaries.
    """
    flat_list_of_projections_details = []
    for projection_list in projection_details.values():
        for projection_data in projection_list:
            if projection_data not in flat_list_of_projections_details:
                flat_list_of_projections_details.append(projection_data)
    return flat_list_of_projections_details


def get_all_table_name_list(projection_details):
    """
    Extracts the all table names from the projections
    """
    table_names_list = []
    for key, projection_list in projection_details.items():
        for projection_data in projection_list:
            if projection_data.get(PowerBITemplateKeys.TABLE_NAME.value) not in table_names_list:
                table_names_list.append(projection_data.get(PowerBITemplateKeys.TABLE_NAME.value))
    return table_names_list


def get_queryref_data(filter_value, column_value, table_name):
    """
    Generates a list of query reference tuples based on the provided filter value, column, and table name.
    This function constructs query reference strings for different date hierarchies (year, quarter, month, day)
    or aggregation functions (sum, count, average, max) depending on the filter_value. The output is a list of
    tuples, where each tuple contains the query reference string and its corresponding label.
    """
    if filter_value in [FilterValues.YR.value,FilterValues.TYR.value]:
        return DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.YEAR.value)
    elif filter_value == FilterValues.QR.value:
        return DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.QUARTER.value)
    elif filter_value == FilterValues.MN.value:
        return DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.MONTH.value)
    elif filter_value == FilterValues.DY.value:
        return DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.DAY.value)
    elif filter_value == FilterValues.TQR.value:
        return [
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.YEAR.value),
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.QUARTER.value)
        ]
    elif filter_value in [FilterValues.TMN.value, FilterValues.TWK.value]:
        return [
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.YEAR.value),
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.QUARTER.value),
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.MONTH.value)
        ]
    elif filter_value == FilterValues.TDY.value:
        return [
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.YEAR.value),
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.QUARTER.value),
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.MONTH.value),
            DATE_QUERY_REF.format(table_name=table_name, column_value=column_value, date_level=FilterValues.DAY.value)
        ]
    elif filter_value in [FilterValues.SUM.value, FilterValues.USR.value]:
        return AGGREGATION_QUERY_REF.format(aggregation=PowerBIReportKeys.SUM.value, table_name=table_name, column_value=column_value)
    elif filter_value in [FilterValues.CNT.value]:
        return AGGREGATION_QUERY_REF.format(aggregation=FilterValues.COUNT.value, table_name=table_name, column_value=column_value)
    elif filter_value in [FilterValues.AVG.value]:
        return AGGREGATION_QUERY_REF.format(aggregation=FilterValues.AVERAGE.value, table_name=table_name, column_value=column_value)
    elif filter_value in [FilterValues.MAX.value]:
        return AGGREGATION_QUERY_REF.format(aggregation=PowerBIReportKeys.MAX.value, table_name=table_name, column_value=column_value)
    else:
        return COLUMNN_QUERY_REF.format(table_name=table_name, column_value=column_value)


def get_select_function(filter_value):
    """
    Determines the selection function index based on the provided filter value.
    """

    if filter_value in [FilterValues.SUM.value, FilterValues.CNT.value, FilterValues.USR.value]: return 0
    elif filter_value in [FilterValues.AVG.value]: return 1
    elif filter_value in [FilterValues.MAX.value]: return 4
    else: return 0


def get_select_json(column, filter_value, table_name):
    """
    Generates a JSON object representing a selection or aggregation expression for a PowerBI report query.
    Depending on the provided filter and column, this function constructs the appropriate JSON structure
    for measures, aggregations, or hierarchy levels (such as Year, Month, Quarter, Day) to be used in PowerBI queries.
    """
    selection_list = []
    native_ref_name = get_nativeReferenceName(filter_value, column)
    query_ref = get_queryref_data(filter_value, column, table_name)
    if filter_value in [FilterValues.SUM.value, FilterValues.CNT.value, FilterValues.USR.value, FilterValues.AVG.value, FilterValues.MAX.value]:
        return [
            json.loads(AGGREGATION_SELECTION.format(
                table_name=table_name, column=column, select_function=get_select_function(filter_value),
                query_ref=query_ref, native_ref_name=native_ref_name)
            )
        ]
    elif filter_value in [FilterValues.YR.value]:
        return [
            json.loads(DATE_HEIRARCHY_SELECTION.format(table_name=table_name, column=column, date_level=FilterValues.YEAR.value, query_ref=query_ref))
        ]
    elif filter_value in [FilterValues.MN.value]:
        return [
            json.loads(DATE_HEIRARCHY_SELECTION.format(table_name=table_name, column=column, date_level=FilterValues.MONTH.value, query_ref=query_ref))
        ]
    elif filter_value in [FilterValues.QR.value]:
        return [
            json.loads(DATE_HEIRARCHY_SELECTION.format(table_name=table_name, column=column, date_level=FilterValues.QUARTER.value, query_ref=query_ref))
        ]
    elif filter_value in [FilterValues.DY.value]:
        return [
            json.loads(DATE_HEIRARCHY_SELECTION.format(table_name=table_name, column=column, date_level=FilterValues.DAY.value, query_ref=query_ref))
        ]
    elif filter_value in [FilterValues.TYR.value,FilterValues.TQR.value, FilterValues.TMN.value, FilterValues.TWK.value, FilterValues.TDY.value]:
        for trunc in TRUNCATED_DATE_TIME.get(filter_value):
            selection_list.extend(get_select_json(column, trunc, table_name))
        return selection_list
    else: return [
        json.loads(COLUMN_SELECTION.format(table_name=table_name, column=column,query_ref=query_ref, native_ref_name=native_ref_name))
    ]


def get_uuid():
    """
    Generates a short unique identifier string.
    Returns:
        str: A 20-character hexadecimal string representing a shortened UUID4.
    """
    
    return uuid.uuid4().hex[:20]


def get_default_worksheet_dimentions():
    """
    Returns the default worksheet dimensions as a list containing a single dictionary.
    """
    return [
            {
                "id": 0,
                "position": TABLEAU_WORKSHEET_DIMENSIONS
            }
        ]
def create_expr(value):
    return {"expr": {"Literal": {"Value": value}}}

def create_color_expr(value):
    return {
        "solid": {
            "color": create_expr(value)
        }
    }

def convert_objects_data_to_powerbi_format(objects_data):
    """
    Converts nested Tableau-like visual config into Power BI object structure.
    Handles special keys like `titleColor`, `labelColor` with theme formatting.
    """
    powerbi_objects = {}

    for key, value in objects_data.items():
        properties = {}

        # For nested keys like valueAxis: {...}
        if isinstance(value, dict):
            for prop_key, prop_val in value.items():
                # Use Theme color structure for specific keys
                if prop_key in [PowerBIObjectKeys.TITLE_COLOR.value, PowerBIObjectKeys.LABEL_COLOR.value]:
                    properties[prop_key] = create_color_expr(prop_val)
                else:
                    properties[prop_key] = create_expr(
                        prop_val if isinstance(prop_val, str) else str(prop_val)
                    )

        elif isinstance(value, str):
            if key == PowerBIObjectKeys.LINE_STYLES.value:
                properties[PowerBIObjectKeys.SHOW_MARKER.value] = create_expr(value)
            elif key == PowerBIObjectKeys.LEGEND.value:
                properties[PowerBIObjectKeys.POSITION.value] = create_expr(value)
            else:
                properties[PowerBIObjectKeys.VALUE.value] = create_expr(value)

        elif isinstance(value, (int, float, bool)):
            properties[PowerBIObjectKeys.VALUE.value] = create_expr(str(value))

        powerbi_objects[key] = [{"properties": properties}]

    return powerbi_objects
