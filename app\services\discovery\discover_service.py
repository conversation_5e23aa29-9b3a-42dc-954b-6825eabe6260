import json
import os
import re
import xml.etree.ElementTree as ET
import requests

from fastapi import <PERSON>TT<PERSON>Exception
from app.core import (
    logger,
    TABLEAU_VERSION,
    XMLNS,
    SIGN_IN_URL,
    POST, GET,
    WORKBOOK_USAGE_STATISTICS_URL,
    <PERSON>OR<PERSON>BOOK_URL, PROJECTS_URL,
    WOR<PERSON>BOOK_DOWNLOAD_URL,
    WOR<PERSON><PERSON>OKS_PATH,
    ClOUD_WORKBOOKS_PATH
)
from app.core.config import S3Config
from app.models_old.reports import ReportDetails, ReportDetailsManager
from uuid import UUID
from app.core.base_service import BaseService
from app.core.response import ServiceResponse
from app.models.tableau_server import TableauServerDetailManager, TableauSiteDetailManager
from app.models.users import UserManager
from app.models.project_details import ProjectDetailManager
from app.schemas.discover import AssignUserRequest


class TableauClient:
    def __init__(self, server_id="", server_url="", pat_name="", pat_token="", site=""):
        self.server_id = server_id
        self.server_url = server_url
        self.pat_name = pat_name
        self.pat_token = pat_token
        self.site = site
        
        
        self.s3 = S3Config()
        self.token = None
        self.site_id = None
        self.user_id = None
        self.site_name = None


    def _encode_response(self, response_text):
        return response_text.encode('ascii', errors="backslashreplace").decode('utf-8')

    def _parse_xml_response(self, response_text):
        return ET.fromstring(self._encode_response(response_text))

    def _make_request(self, method, url, headers=None, data=None):
        logger.info(f"Making {method} request to URL: {url}")
        try:
            response = requests.request(method, url, headers=headers, data=data)
            response.raise_for_status()
            logger.debug(f"Received response status_code: {response.status_code}")
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise HTTPException(status_code=response.status_code, detail=f"Request failed: {e}")

    def sign_in(self):
        logger.info("Signing in to Tableau server")
        url = SIGN_IN_URL.format(server=self.server_url, version=TABLEAU_VERSION)
        xml_request = ET.Element('tsRequest')
        credentials = ET.SubElement(xml_request, 'credentials',
                                     personalAccessTokenName=self.pat_name,
                                     personalAccessTokenSecret=self.pat_token)
        ET.SubElement(credentials, 'site', contentUrl=self.site)

        headers = {'Content-Type': 'application/xml'}
        parsed_response = self._parse_xml_response(
            self._make_request(POST, url, headers=headers, data=ET.tostring(xml_request)).text
        )

        self.token = parsed_response.find('t:credentials', namespaces=XMLNS).get('token')
        self.user_id = parsed_response.find('.//t:user', namespaces=XMLNS).get('id')
        site = parsed_response.find('.//t:site', namespaces=XMLNS)
        self.site_id = site.get('id')
        self.site_name = site.get('contentUrl')

    def get_projects(self):
        url = PROJECTS_URL.format(server_url=self.server_url, version=TABLEAU_VERSION, site_id=self.site_id)
        headers = {'X-Tableau-Auth': self.token}
        parsed_response = self._parse_xml_response(self._make_request(GET, url, headers=headers).text)
        return [
            {
                "site_id": self.site_id,
                "project_id": proj.get('id'),
                "project_name": proj.get('name'),
                "parent_project_id": proj.get('parentProjectId')
            } for proj in parsed_response.findall('.//t:project', namespaces=XMLNS)
        ]

    def get_workbook_usage(self, workbook_id):
        url = WORKBOOK_USAGE_STATISTICS_URL.format(server_url=self.server_url, workbook_id=workbook_id)
        headers = {'X-Tableau-Auth': self.token}
        usage = self._make_request(GET, url, headers=headers).text
        return json.loads(usage).get("hitsTotal", 0)

    async def download_and_upload_workbook(self, workbook_id, organization_name, workbook_name):
        """
        Downloads a workbook from Tableau and uploads it to S3 using the workbook_name as the filename.
        """
        url = WORKBOOK_DOWNLOAD_URL.format(
            server_url=self.server_url,
            version=TABLEAU_VERSION,
            site_id=self.site_id,
            workbook_id=workbook_id
        )

        headers = {'x-tableau-auth': self.token}
        response = self._make_request(GET, url, headers=headers)

        # Always use the workbook_name from the schema, fallback to workbook_id if missing
        filename = f"{workbook_name}.twbx" if workbook_name else f"{workbook_id}.twbx"

        os.makedirs(WORKBOOKS_PATH, exist_ok=True)
        file_path = os.path.join(WORKBOOKS_PATH, filename)

        with open(file_path, 'wb') as f:
            f.write(response.content)

        logger.info(f"Workbook {workbook_id} saved to {file_path}")

        s3_key = f"BI-PortV3/{organization_name}/{workbook_id}/tableau_file/{filename}"
        success = await self.s3.upload_to_s3(file_path=file_path, object_name=s3_key)
        if success:
            logger.info(f"Workbook {workbook_id} uploaded to S3 at {s3_key}")
            os.remove(file_path)
            logger.info(f"Local file {file_path} deleted after S3 upload.")
        else:
            logger.error(f"Failed to upload {file_path} to S3 at {s3_key}. File not deleted.")
        return s3_key

    async def get_workbooks(self, organization_name):
        url = WORKBOOK_URL.format(server_url=self.server_url, version=TABLEAU_VERSION, site_id=self.site_id)
        headers = {'X-Tableau-Auth': self.token}
        parsed_response = self._parse_xml_response(self._make_request(GET, url, headers=headers).text)

        workbooks = []
        for wb in parsed_response.findall('.//t:workbook', namespaces=XMLNS):
            project_elem = wb.find(".//t:project", namespaces=XMLNS)
            project_id = project_elem.get("id") if project_elem is not None else None
            project_name = project_elem.get("name") if project_elem is not None else None

            workbook_id = wb.get("id") if wb is not None else None
            workbook_name = wb.get("name") if wb is not None else None
            usage = self.get_workbook_usage(workbook_id) if workbook_id else 0
            await self.download_and_upload_workbook(workbook_id, organization_name, workbook_name)

            workbooks.append({
                "site_id": self.site_id,
                "workbook_id": workbook_id,
                "workbook_name": workbook_name,
                "location_id": project_id,
                "location_name": project_name,
                "usage": int(usage)
            })
        return workbooks

    async def get_site_data(self, organization_name):
        self.sign_in()
        projects = self.get_projects()
        workbooks = await self.get_workbooks(organization_name)

        project_map = {
            p["project_id"]: {**p, "sub_projects": [], "workbooks": []}
            for p in projects
        }

        top_level = []
        for proj in project_map.values():
            if proj["parent_project_id"] and proj["parent_project_id"] in project_map:
                project_map[proj["parent_project_id"]]["sub_projects"].append(proj)
            else:
                top_level.append(proj)

        for wb in workbooks:
            if wb["location_id"] in project_map:
                project_map[wb["location_id"]]["workbooks"].append(wb)

        report_data = [
            ReportDetails(
                workbook_id=wb["workbook_id"],
                report_name=wb["workbook_name"],
                server_id=self.server_id
            )
            for wb in workbooks
        ]

        ReportDetailsManager.bulk_insert_reports(report_data)

        project_summary = [{
            "server_id": str(self.server_id),
            "project_id": p["project_id"],
            "project_name": p["project_name"],
            "number_of_workbooks": len(p["workbooks"]),
            "workbooks": [
                {"workbook_id": wb["workbook_id"], "workbook_name": wb["workbook_name"]}
                for wb in p["workbooks"]
            ]
        } for p in project_map.values()]

        return {
            "site_name": self.site_name,
            "projects": top_level,
            "projects_count": len(projects),
            "workbooks_count": len(workbooks),
            "project_summary": project_summary
        }

    def calculate_usage(self, project):
        sub_usage = sum(self.calculate_usage(sub) for sub in project["sub_projects"])
        own_usage = sum(wb.get("usage", 0) for wb in project.get("workbooks", []))
        project["usage"] = sub_usage + own_usage
        return project["usage"]
    
class DiscoverService(BaseService):

    def _get_all_servers(self, organization_id: UUID, page: int, page_size: int) -> ServiceResponse:
        offset = (page - 1) * page_size

        servers = TableauServerDetailManager.get_servers_by_org_id(
            organization_id=organization_id,
            offset=offset,
            limit=page_size
        )

        response_data = [
            {
                "server_id": str(server.id),
                "server_name": server.name
            }
            for server in servers
        ]

        # Add static value
        response_data.append({"server_id": "0", "server_name": "upload_files"})

        return ServiceResponse.success(data=response_data)

    def _get_all_sites(self, server_id: UUID, page: int, page_size: int) -> ServiceResponse:
        offset = (page - 1) * page_size

        sites = TableauSiteDetailManager.get_sites_by_server_id(
            server_id=server_id,
            offset=offset,
            limit=page_size
        )

        response_data = [
            {
                "site_id": str(site.id),
                "site_name": site.site_name,
                "credentials_id": str(site.credentials_id),
                "server_id": str(site.credentials.server_id)
            }
            for site in sites
        ]

        return ServiceResponse.success(data=response_data)

    
    def _get_all_managers(self,organization_id: UUID,page: int,page_size: int) -> ServiceResponse:
        offset = (page - 1) * page_size

        managers = UserManager.get_managers_by_org_id(
            organization_id=organization_id,
            page=page,
            page_size=page_size
        )

        response_data = [
            {
                "manager_id": str(manager.id),
                "manager_name": manager.name
            }
            for manager in managers
        ]

        return ServiceResponse.success(data=response_data)

    def _get_all_developers(self,organization_id: UUID,manager_id: UUID,page: int,page_size: int) -> ServiceResponse:
        developers = UserManager.get_developers_by_org_and_manager_id(organization_id=organization_id,manager_id=manager_id,
            page=page,
            page_size=page_size
        )

        response_data = [
            {
                "developer_id": str(dev.id),
                "developer_name": dev.name,
                "manager_id": str(dev.manager_id),
            }
            for dev in developers
        ]

        return ServiceResponse.success(data=response_data)

    def _get_all_root_projects(self, page: int, page_size: int) -> ServiceResponse:
        projects = ProjectDetailManager.get_all_root_projects(page=page, page_size=page_size)

        response_data = [
            {
                "id": str(project.id),
                "name": project.name,
                "is_upload": project.is_upload,
                "site_id": str(project.site_id),
                "server_id": str(project.server_id),
                "parent_id": str(project.parent_id) if project.parent_id else None,
                "user_id": str(project.user_id),
                "assigned_to": str(project.assigned_to) if project.assigned_to else None
            }
            for project in projects
        ]

        return ServiceResponse.success(data=response_data)
    
    def _update_project_assigned_to(self, project_id: UUID, user_id: UUID) -> ServiceResponse:
        project = ProjectDetailManager.assign_user_to_project(
            project_id=project_id,
            user_id=user_id
        )

        if not project:
            return ServiceResponse.failure("Project not found", status_code=404)

        assigned_user = project.assignee
        if not assigned_user:
            return ServiceResponse.failure("Assigned user not found", status_code=404)

        return ServiceResponse.success(
            data={
                "project_id": str(project.id),
                "user_id": str(project.user_id),
                "assigned_to": {
                    "id": str(assigned_user.id),
                    "assigned_user": assigned_user.name,
                    "role_id": str(assigned_user.role_id),
                    "manager_id": str(assigned_user.manager_id)
                },
                "message": "User assigned successfully"
            }
        )

    def _get_all_projects_by_site(self, site_id: UUID) -> ServiceResponse:
        root_projects, sub_projects, reports, assigned_users_map = ProjectDetailManager.get_projects_by_site(site_id)

        report_map = {}
        for report in reports:
            report_map.setdefault(report.project_id, []).append({
                "id": str(report.id),
                "file_name": report.name,
                "project_id": str(report.project_id),
                "view_count": report.view_count or 0
            })

        sub_project_map = {}
        for sp in sub_projects:
            assigned_user = assigned_users_map.get(sp.assigned_to)
            assigned_data = {
                "id": str(assigned_user.id),
                "assigned_user": assigned_user.name,
                "role_id": str(assigned_user.role_id),
                "manager_id": str(assigned_user.manager_id) if assigned_user.manager_id else None
            } if assigned_user else {}

            sub_project_map.setdefault(sp.parent_id, []).append({
                "project_id": str(sp.id),
                "project_name": sp.name,
                "parent_id": str(sp.parent_id),
                "files": report_map.get(sp.id, [])
            })

        result = []
        for parent in root_projects:
            assigned_user = assigned_users_map.get(parent.assigned_to)
            assigned_data = {
                "id": str(assigned_user.id),
                "assigned_user": assigned_user.name,
                "role_id": str(assigned_user.role_id),
                "manager_id": str(assigned_user.manager_id) if assigned_user.manager_id else None
            } if assigned_user else {}

            result.append({
                "project_id": str(parent.id),
                "project_name": parent.name,
                "parent_id": None,
                "assigned_to": assigned_data,
                "sub_projects": sub_project_map.get(parent.id, [])
            })

        return ServiceResponse.success(data={"projects": result})


    def _get_projects_by_parent_id(self, parent_id: UUID) -> ServiceResponse:
        sub_projects, reports = ProjectDetailManager.get_projects_by_parent(parent_id)

        report_map = {}
        for report in reports:
            report_map.setdefault(report.project_id, []).append({
                "id": str(report.id),
                "file_name": str(report.name),
                "project_id": str(report.project_id),
                "view_count": report.view_count
            })

        result = []
        for sp in sub_projects:
            result.append({
                "id": str(sp.id),
                "name": sp.name,
                "parent_id": str(sp.parent_id),
                "assigned_to": {
                    "id": str(sp.assigned_to) if sp.assigned_to else None,
                    "assigned_user": sp.assigned_to_name,
                    "role_id": str(sp.role_id) if sp.role_id else None,
                    "manager_id": str(sp.manager_id) if sp.manager_id else None
                },
                "files": report_map.get(sp.id, [])
            })

        return ServiceResponse.success(data={"sub-projects": result})

        
    def _get_all_developers_by_org_id(self, organization_id: UUID, page: int, page_size: int) -> ServiceResponse:
        offset = (page - 1) * page_size

        developers = UserManager.get_developers_by_org_id(
            organization_id=organization_id,
            page=page,
            page_size=page_size
        )

        response_data = [
            {
                "developer_id": str(developer.id),
                "developer_name": developer.name
            }
            for developer in developers
        ]

        return ServiceResponse.success(data={"sub-projects": response_data})

  
    def _get_all_developers_by_orgid(self, organization_id: UUID, page: int, page_size: int) -> ServiceResponse:
        offset = (page - 1) * page_size

        developers = UserManager.get_developers_by_org_id(
            organization_id=organization_id,
            page=page,
            page_size=page_size
        )

        response_data = [
            {
                "developer_id": str(developer.id),
                "developer_name": developer.name
            }
            for developer in developers
        ]

        return ServiceResponse.success(data=response_data)
