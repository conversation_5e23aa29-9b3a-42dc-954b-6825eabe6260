from ..core import (
    get_projections_data, get_title_config, get_background_config, get_from_list,
    get_border_config, process_tooltip_data, to_list, create_expr,
    process_label_data, process_format_data, process_pane_encodings
)
from app.core import line_chart_json
from app.core import logger
from app.core.enums import VisualRequest
from app.services.migrate.Tableau_Analyzer.report import (
    generate_projections_data, extract_multiple_encodings_data, remove_duplicate_fields, process_format_data_new, process_label_data_new

)
from app.core.enums import TableauXMLTags, PowerBITemplateKeys, PowerBIReportKeys, PowerBIChartTypes, PowerBIObjectKeys
import json
import uuid



def get_linechart_objects_data(style_rule, rows, cols, y2_data):
    objects = {}
    objects["lineStyles"] = [{"properties": {"showMarker": create_expr("true")}}]
    objects["legend"] = [{"properties":{"position":create_expr(f"'Right'")}}]
    label_data = process_label_data(style_rule)
    if label_data: objects["labels"] = [{"properties":label_data}]
    if style_rule:
        rows_category_axis = process_format_data(style_rule, rows, "rows")
        cols_value_axis = process_format_data(style_rule, cols, "cols")
        y2_axis = process_format_data(style_rule, y2_data, "rows")
        if rows and rows_category_axis: objects["categoryAxis"] = [{"properties":rows_category_axis}]
        if cols and cols_value_axis: objects["valueAxis"] = [{"properties":cols_value_axis}]
        if y2_data and y2_axis: objects["y2Axis"] = [{"properties":y2_axis}]
    return objects

def fetch_objects_data(style_rule, rows, cols, y2_data=None):
    objects = {}
    objects[PowerBIObjectKeys.LINE_STYLES.value] = PowerBIObjectKeys.TRUE.value
    objects[PowerBIObjectKeys.LEGEND.value] = f"'Right'"

    label_data = process_label_data_new(style_rule)
    if label_data:
        objects[PowerBIObjectKeys.LABELS.value] = label_data
    

    if style_rule:
        rows_category_axis = process_format_data_new(style_rule, rows, "rows")
        cols_value_axis = process_format_data_new(style_rule, cols, "cols")
        y2_axis = process_format_data_new(style_rule, y2_data, "rows")

        if rows and rows_category_axis:
            objects[PowerBIObjectKeys.VALUE_AXIS.value] = rows_category_axis
        if cols and cols_value_axis:
            objects[PowerBIObjectKeys.CATEGORY_AXIS.value] = cols_value_axis
        if y2_data and y2_axis:
            objects[PowerBIObjectKeys.Y2_AXIS.value] = y2_axis
    return objects


def get_linechart_report(rows, cols, table_column_data, panes, datasource_col_list, worksheet_name, worksheet_title_layout, style):
    try:
        overall_line_chart_result = []

        projections_dict = {}

        color_field, tooltips, lod_data, text_data = process_pane_encodings(panes)

        if color_field and 'Measure Names' in color_field:
            color_field = rows

        style_rule = to_list(style.get("style-rule")) if style else None
        y2_data = None

        if style_rule:
            for rule in style_rule:
                if rule.get("@element") == "axis":
                    y2_data = rule.get("encoding",{}).get("@field")

        tooltips_data = process_tooltip_data(data = [tooltips, lod_data, text_data], visual_fields= [rows, cols, y2_data, color_field])

        result_queryref, table_list, select_list = get_projections_data([rows, cols, color_field, y2_data, tooltips_data], table_column_data, datasource_col_list)
        y_queryref_list = result_queryref.get(rows)
        category_queryref_list = result_queryref.get(cols)
        series_queryref_list = result_queryref.get(color_field)
        y2_queryref_list = result_queryref.get(y2_data)
        tooltip_list = result_queryref.get(tooltips_data)

        projections_dict["Y"] = y_queryref_list
        projections_dict["Category"] = category_queryref_list
        if not y2_queryref_list and series_queryref_list: projections_dict["Series"] = series_queryref_list
        if tooltip_list: projections_dict["Tooltips"] = tooltip_list
        if y2_queryref_list: projections_dict["Y2"]= y2_queryref_list

        from_list = get_from_list(table_list)

        title_list = get_title_config(worksheet_title_layout, style)
        background_list = get_background_config(style= style)
        objects_data = get_linechart_objects_data(style_rule, rows, cols, y2_data)

        linechart_json = {
                "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
                "projection_data" : json.dumps(projections_dict),
                "from_list" : json.dumps(from_list),
                "select_list" : json.dumps(select_list),
                "title_list" : json.dumps(title_list),
                "background_list" : json.dumps(background_list),
                "border_list" : get_border_config(),
                "objects_data" : json.dumps(objects_data)
            }

        overall_line_chart_result.append({"config":linechart_json, "template":line_chart_json})
        return overall_line_chart_result

    except Exception as e:
        logger.error(f"---Error in generating line chart visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating line chart visual for {worksheet_name} - {str(e)}")
    
def get_secondary_axis(style_data):
    '''retrieves the value of 'field' from 'encoding' tag in styles for secondary axis'''
    return style_data.get(TableauXMLTags.AXIS.value, {}).get(TableauXMLTags.ENCODING.value, {}).get(TableauXMLTags.FIELD.value,"") if style_data else ""


def process_line_chart_report(request: VisualRequest):
    line_chart_result = {}
    panes = request.panes
    rows = request.rows
    cols = request.cols
    table_column_data = request.table_column_data
    calculations_related_data = request.calculations_related_data
    style_data = request.worksheet_style_data

    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )

    unique_tooltips = remove_duplicate_fields(
        encodings[TableauXMLTags.TOOLTIP.value],
        encodings[TableauXMLTags.LOD.value],
        encodings[TableauXMLTags.TEXT.value],
        rows,
        cols
    )
    unique_color_data = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])
    secondary_axis = get_secondary_axis(style_data)

    linechart_field_mapping = {
        PowerBIReportKeys.Y.value: rows,
        PowerBIReportKeys.CATEGORY.value: cols,
    }

    if unique_tooltips: linechart_field_mapping[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips
    if not secondary_axis and unique_color_data: linechart_field_mapping[PowerBIReportKeys.SERIES.value] = unique_color_data
    if secondary_axis: linechart_field_mapping[PowerBIReportKeys.Y2.value] = [secondary_axis]

    projections_data = generate_projections_data(table_column_data, calculations_related_data, linechart_field_mapping)

    line_chart_result[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.LINE_CHART.value
    line_chart_result[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    line_chart_result[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data
    return line_chart_result
