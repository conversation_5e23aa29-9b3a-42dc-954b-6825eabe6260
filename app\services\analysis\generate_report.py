import os
from pathlib import Path
import shutil
import json
from typing import List, Dict, <PERSON><PERSON>
from docx import Document
from uuid import UUID
from docx.shared import Pt
from docx.oxml.ns import qn
from fastapi import HTTPException, Request
from starlette.concurrency import run_in_threadpool
from app.models.users import User
from app.core.enums import (
    GeneralKeys as GS,
    Datasource as DS
)
from app.core import AUTOMATIC_MIGRATED_CHARTS, logger
from app.core.constants import S3_URL_EXPIRATION_SECONDS, REPORT_PATH, ANALYZED_OUTPUTS_PATH, ANALYZED_OUTPUTS_DIR,S3_BASE_PATH
from app.services.analysis.datasource import get_datasource_type

from .s3_service import download_twb_files_from_s3_by_id, zip_output_files
from .generate_document import (
    generate_reports_from_json,
    set_table_borders,
    set_header_bottom_border
)
from .process_twbfiles import process_twb_files
from .dashboard import get_dashboard_worksheet_data, determine_report_complexity
from app.core.config import S3Config
from app.models_old.upload_file_report_details import UploadFilesReportManager
from app.models.report_details import ReportDetail, ReportDetailManager
from app.core.exceptions import BadRequestError


s3_config = S3Config()
bucket_name = s3_config.bucket_name

async def generate_report(report_id: UUID, user: User) -> dict:
    logger.info("[generate_report] Starting report generation for report_id: %s", str(report_id))

    local_dir = None
    try:
        # Fetch report record using ReportDetailManager
        record = await run_in_threadpool(ReportDetailManager.get_report_by_id, report_id)
        if not record:
            raise BadRequestError(detail=f"Report with ID {report_id} not found")

        # Extract needed fields
        report_name = record.name.strip()
        project_id = record.project_id
        s3_report_id = record.report_id
        # Use current user's organization name exactly as given (no strip)
        organization_name = user.organization.name
        
        # Extract base name without extension if it exists
        # Some report names in DB already have .twb or .twbx extension
        if report_name.lower().endswith(('.twb', '.twbx')):
            base_report_name = os.path.splitext(report_name)[0]
        else:
            base_report_name = report_name

        # Use organization name exactly as given for analyzed output
        
        s3_zip_key = f"{ANALYZED_OUTPUTS_PATH}/{base_report_name}.zip"

        # Already analyzed: return pre-signed URL
        if record.is_analyzed:
            # Generate presigned URL for the analyzed file
            zip_url = await s3_config.generate_presigned_url(object_key=s3_zip_key)
            return {
                "report_id": str(report_id),
                "report_name": report_name,
                "download_url": zip_url,
                "report_path": ReportDetailManager.get_report_hierarchy_path(project_id, report_name),
                "analysed_status": record.analyzed_status
            }

        # Not analyzed: perform analysis
        input_dir, local_dir, unique_folder, _ = (
            await download_twb_files_from_s3_by_id(report_id, user)
        )

        output_dir = os.path.join(local_dir, 'analysed_output_files')
        os.makedirs(output_dir, exist_ok=True)

        # Process and generate structure
        process_twb_files(input_dir, output_dir)
        generate_reports_from_json(output_dir, user, base_report_name)

        # Zip and upload
        zip_url, zip_name = await zip_output_files(base_report_name, unique_folder, output_dir, local_dir)
        logger.info(f"[generate_report] Zip created at {zip_name}")


        # Mark as analyzed using ReportDetailManager
        await run_in_threadpool(ReportDetailManager.mark_analyzed, str(report_id), "SUCCESS", "Analysis completed successfully")

        return {
            "report_id": str(report_id),
            "report_name": report_name,
            "download_url": zip_url,
            "report_path": ReportDetailManager.get_report_hierarchy_path(project_id, report_name),
            "analysed_status": record.analyzed_status
        }

    except Exception as e:
        logger.error(f"Error during report generation for {report_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

    finally:
        if local_dir:
            try:
                shutil.rmtree(local_dir)
                logger.info(f"Cleaned up local directory: {local_dir}")
            except Exception as cleanup_error:
                logger.warning(f"Cleanup failed: {cleanup_error}")



def apply_formatting(paragraph, bold=False, font_size=GS.DEFAULT_FONT_SIZE_PT.value):
    if paragraph.runs:
        try:
            run = paragraph.runs[0]
            run.font.name = GS.FONT_NAME.value
            r = run._element
            r.rPr.rFonts.set(qn('w:eastAsia'), GS.FONT_NAME.value)
            run.font.size = Pt(font_size)
            if bold:
                run.bold = True
        except Exception as e:
            logger.error(f"Error applying formatting: {e}")


def generate_summary(workbook_name, folder_path: str) -> Tuple[int, int]:
    logger.info(f"Generating summary for: {folder_path}")

    try:
        doc = Document()
        doc.add_heading("Summary", level=0)

        datasource_names = {}
        dashboard_names = []
        worksheet_types_count = {}
        total_dashboards = 0
        total_visuals = 0
        total_calculated_fields = 0
        total_worksheets = 0
        automatic_migration_count = 0
        manual_migration_count = 0
        calculated_fields_summary = []
        dashboard_worksheet_names = {}
        report_complexity_data = {}
        total_datasources = 0

        chart_types_path = os.path.join(folder_path, "chart_types.json")

        if chart_types_path and os.path.exists(chart_types_path):
            with open(chart_types_path, 'r') as chart_types_file:
                    chart_types_json = json.load(chart_types_file)
                    if isinstance(chart_types_json, dict) and "chart_types" in chart_types_json.keys():
                        chart_list = chart_types_json["chart_types"]
                        if isinstance(chart_list, list):
                            for chart in chart_list:
                                chart_type = chart.get("chart_type")
                                if chart_type:
                                    if chart_type in AUTOMATIC_MIGRATED_CHARTS:
                                        automatic_migration_count += 1
                                    else:
                                        manual_migration_count += 1
                                    worksheet_types_count[chart_type] = worksheet_types_count.get(chart_type, 0) + 1


            total_visuals = sum(worksheet_types_count.values())

        # File paths (adapt names if necessary)
        datasource_path = os.path.join(folder_path, "datasources.json")
        worksheet_path = os.path.join(folder_path, "worksheets.json")
        dashboard_path = os.path.join(folder_path, "dashboards.json")

        folder_name = os.path.basename(folder_path)

        # Load datasource info
        if os.path.exists(datasource_path):
            with open(datasource_path, 'r') as ds_file:
                datasource_json = json.load(ds_file)
                connection_details = datasource_json.get(DS.Datasources.value, {}).get(GS.CONNECTION_DETAILS.value, [])

                for connection in connection_details:
                    file_name = connection.get('filename', '').strip()
                    ds_name = os.path.splitext(os.path.basename(file_name))[0] if file_name else GS.UNKNOWN_DATASOURCE.value

                    conn_type = connection.get(GS.CONNECTION_TYPE.value, 'Unknown')

                    extract_class = connection.get(GS.EXTRACT_CLASS.value, '').strip().lower()
                    class_attr = connection.get(GS.CLASS.value, '').strip().lower()

                    if extract_class == GS.HYPER.value or class_attr == GS.HYPER.value:
                        ds_type = GS.HYPER_FILE.value
                    elif file_name.lower().endswith(('.xls', '.xlsx')):
                        ds_type = GS.EXCEL.value
                    elif file_name.lower().endswith('.csv'):
                        ds_type = GS.CSV_OR_TEXT_FILE.value
                    else:
                        ds_type = get_datasource_type(class_attr or extract_class)

                    datasource_names[ds_name] = (ds_type, conn_type)

                # Calculated fields
                calculations = datasource_json.get(DS.Datasources.value, {}).get(GS.CALCULATIONS.value, [])
                num_calculated_fields = len(calculations)
                total_calculated_fields += num_calculated_fields

                calculated_fields_summary.append({
                    'file_name': workbook_name,
                    'calculated_fields_count': num_calculated_fields
                })

        # Load worksheets count
        if os.path.exists(worksheet_path):
            with open(worksheet_path, 'r') as ws_file:
                worksheet_json = json.load(ws_file)
                if isinstance(worksheet_json, dict):
                    worksheets = worksheet_json.get(GS.WorkSheets.value, [])
                elif isinstance(worksheet_json, list):
                    worksheets = worksheet_json
                else:
                    worksheets = []
                total_worksheets += len(worksheets)

        # Load dashboards info
        if os.path.exists(dashboard_path):
            with open(dashboard_path, 'r') as db_file:
                dashboard_json = json.load(db_file)
                dashboards = dashboard_json.get(GS.Dashboards.value, [])
                total_dashboards += len(dashboards)
                dashboard_names.extend([dashboard.get(GS.NAME.value, '') for dashboard in dashboards])
                dashboard_worksheet_data = get_dashboard_worksheet_data(dashboard_json, folder_name)
                dashboard_worksheet_names.update(dashboard_worksheet_data)
        
        # Calculate complexity per report
        complexity = determine_report_complexity(total_visuals, total_calculated_fields, total_datasources, manual_migration_count)
        report_complexity_data[folder_name] = complexity

        # ======= Build Document =======

        # Summary of All Files
        doc.add_heading("Summary of All Files", level=1)
        doc.add_paragraph(f"Total Number of Dashboards: {len(dashboard_names)}")
        if dashboard_names:
            doc.add_heading("Dashboard Names:", level=2)
            for name in dashboard_names:
                doc.add_paragraph(name, style="List Bullet")

        # Report Complexity Summary Table
        doc.add_heading("Report Complexity Summary", level=2)
        complexity_table = doc.add_table(rows=1, cols=2)

        hdr_cells = complexity_table.rows[0].cells
        hdr_cells[0].text = 'Report Name'
        hdr_cells[0].paragraphs[0].runs[0].bold = True
        hdr_cells[1].text = 'Complexity Type'
        hdr_cells[1].paragraphs[0].runs[0].bold = True

        set_header_bottom_border(hdr_cells[0])
        set_header_bottom_border(hdr_cells[1])

        for report_name, comp in report_complexity_data.items():
            row_cells = complexity_table.add_row().cells

            if len(row_cells) >= 2:
                row_cells[0].text = workbook_name#str(report_name) if report_name is not None else "N/A"
                row_cells[1].text = str(comp) if comp is not None else "N/A"
            else:
                logger.warning(f"Expected 2 cells in complexity table row, got {len(row_cells)}")

        set_table_borders(complexity_table)


        # Visual Types Summary
        if worksheet_types_count:
            doc.add_heading("Visual Types Summary", level=2)
        doc.add_paragraph(f"Total Number of Visuals: {total_visuals}")
        doc.add_paragraph(f"Automatic Migration Possibility: {automatic_migration_count}")
        doc.add_paragraph(f"Manual Migration Possibility: {manual_migration_count}")
        
        table = doc.add_table(rows=1, cols=5)

        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Report Name'
        hdr_cells[0].paragraphs[0].runs[0].bold = True
        hdr_cells[1].text = 'Dashboard Name'
        hdr_cells[1].paragraphs[0].runs[0].bold = True
        hdr_cells[2].text = 'Visual Name'
        hdr_cells[2].paragraphs[0].runs[0].bold = True
        hdr_cells[3].text = 'Visual Type'
        hdr_cells[3].paragraphs[0].runs[0].bold = True
        hdr_cells[4].text = 'Migration'
        hdr_cells[4].paragraphs[0].runs[0].bold = True

        set_header_bottom_border(hdr_cells[0])
        set_header_bottom_border(hdr_cells[1])
        set_header_bottom_border(hdr_cells[2])
        set_header_bottom_border(hdr_cells[3])
        set_header_bottom_border(hdr_cells[4])

        # Assuming chart_types_json is already loaded as above
        if isinstance(chart_types_json, dict):
            file_name = chart_types_json.get("filename", "")
            file_name = os.path.splitext(file_name)[0]
            charts = chart_types_json.get("chart_types", [])
        
            for item in charts:
                chart_type = item.get("chart_type", "")
                row_cells = table.add_row().cells
                row_cells[0].text = workbook_name
                row_cells[1].text = dashboard_worksheet_data.get(file_name, {}).get(item.get("name", ""), "No Dashboard")
                row_cells[2].text = item.get("name", "")
                row_cells[3].text = chart_type
                row_cells[4].text = "Biport" if chart_type in AUTOMATIC_MIGRATED_CHARTS else "Manual"

            
            set_table_borders(table)

        total_datasources = len(datasource_names)

        # Datasource Summary
        doc.add_paragraph(f"\nTotal Number of Datasources: {total_datasources}")
        if datasource_names:
            doc.add_heading("Datasource Names:", level=2)
            datasource_table = doc.add_table(rows=1, cols=3)
            hdr_cells = datasource_table.rows[0].cells
            hdr_cells[0].text = 'Datasource Name'
            hdr_cells[0].paragraphs[0].runs[0].bold = True
            hdr_cells[1].text = 'Datasource Type'
            hdr_cells[1].paragraphs[0].runs[0].bold = True
            hdr_cells[2].text = 'Connection Type'
            hdr_cells[2].paragraphs[0].runs[0].bold = True

            set_header_bottom_border(hdr_cells[0])
            set_header_bottom_border(hdr_cells[1])
            set_header_bottom_border(hdr_cells[2])

            for cell in hdr_cells:
                cell.paragraphs[0].runs[0].bold = True
                set_header_bottom_border(cell)

            for name, (ds_type, conn_type) in datasource_names.items():
                row_cells = datasource_table.add_row().cells
                row_cells[0].text = name
                row_cells[1].text = get_datasource_type(ds_type)
                row_cells[2].text = conn_type


            set_table_borders(datasource_table)

        # Calculated Fields Summary
        doc.add_paragraph(f"\nTotal Calculated Fields Across All Files: {total_calculated_fields}")
        doc.add_heading("Calculated Fields Summary", level=2)
        cf_table = doc.add_table(rows=1, cols=2)
        hdr_cells = cf_table.rows[0].cells
        hdr_cells[0].text = 'File Name'
        hdr_cells[0].paragraphs[0].runs[0].bold = True
        hdr_cells[1].text = 'Number of Calculated Fields'
        hdr_cells[1].paragraphs[0].runs[0].bold = True

        set_header_bottom_border(hdr_cells[0])
        set_header_bottom_border(hdr_cells[1])

        for summary in calculated_fields_summary:
            row_cells = cf_table.add_row().cells
            row_cells[0].text = summary['file_name']
            row_cells[1].text = str(summary['calculated_fields_count'])

        set_table_borders(cf_table)

        # Save the document
        summary_path = os.path.join(folder_path, "Summary.docx")
        doc.save(summary_path)
        logger.info(f"summary saved to {summary_path}")

    except Exception as e:
        logger.error(f"Error generating summary for {folder_path}: {e}", exc_info=True)
        raise

    return total_worksheets, total_calculated_fields, total_dashboards, total_datasources
