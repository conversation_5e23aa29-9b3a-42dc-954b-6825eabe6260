import os
import xml.etree.ElementTree as ET
import json
from app.core.enums import (
    WorkSheet as WS,
    GeneralKeys as GS,
    ChartType
)
from app.services.analysis.visuals.bar import Direct

from .visuals import check_functions
from app.core import logger


from .visuals import check_functions
from app.core.config import logger
from app.core.enums import ChartType

def chart_type(worksheet, chart_types):
    for check_function in check_functions:
        response = check_function(worksheet)
        if response[GS.STATUS.value]:
            chart_types.append({GS.NAME.value: worksheet.get(WS.NAME.value),"chart_type": response["chart_type"]})
            return

    chart_types.append({GS.NAME.value: worksheet.get(WS.NAME.value),"chart_type": Direct.get_direct_chart_type(worksheet)})


def get_chart_types(xml_file_path):
    try:
        logger.info(f"Analyzing chart types in {xml_file_path}")
        chart_types = []

        tree = ET.parse(xml_file_path)
        root = tree.getroot()

        for worksheet in root.findall(WS.WORKSHEETS.value):
            chart = Direct.get_direct_chart_type(worksheet)
            if (
                chart == ChartType.AUTOMATIC.value or
                chart == ChartType.MIXED_CHART.value
            ):
                chart_type(worksheet, chart_types)
            else:
                chart_types.append(
                    {
                        GS.NAME.value: worksheet.get(WS.NAME.value),
                        "chart_type": chart
                    }
                )

        return chart_types

    except ET.ParseError as e:
        logger.error(f"Error parsing XML file {xml_file_path}: {e}")
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")


def analyse_Chart_types(
    input_dir,
    output_dir,
    file_extension=GS.TXT_FILE.value
):
    logger.info("Analyzing chart types.")

    if not os.path.exists(input_dir):
        logger.error(f"Input directory '{input_dir}' does not exist.")
        return {GS.ERROR.value: f"Directory '{input_dir}' does not exist."}

    if not os.path.exists(output_dir):
        logger.info(f"Output directory '{output_dir}' \
                    does not exist. Creating it now.")
        return {GS.ERROR.value: f"Directory '{output_dir}' does not exist."}
    
    output_file_path = None

    for filename in os.listdir(input_dir):
        if filename.endswith(file_extension):
            xml_file_path = os.path.join(input_dir, filename)
            folder_name = os.path.splitext(filename)[0]
            folder_path = os.path.join(output_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True)
            output_file_path = os.path.join(folder_path, "chart_types.json")
            try:
                json_data = {
                    GS.FILE_NAME.value: filename,
                    GS.CHART_TYPES.value: get_chart_types(xml_file_path)
                }
            except Exception as e:
                logger.error(f"Error processing file '{filename}': {e}")
                json_data.update(
                    {
                        GS.FILE_NAME.value: filename,
                        GS.ERROR.value: str(e)
                    }
                )
            try:
                with open(output_file_path, 'w') as f:
                    json.dump(json_data, f, indent=4)
                logger.info(f"Chart types analysis completed. Results saved to \
                            '{output_file_path}'.")
            except Exception as e:
                logger.error(f"Failed to write output file '{output_file_path}': {e}")
                return {GS.ERROR.value: str(e)}

    return {
        "message": "Analysis completed successfully.",
        "output_file": output_file_path
    }
