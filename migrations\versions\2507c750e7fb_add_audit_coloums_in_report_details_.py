"""Add audit coloums in report details table

Revision ID: 2507c750e7fb
Revises: e8118d5f0e52
Create Date: 2025-07-16 15:40:38.778557

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2507c750e7fb'
down_revision: Union[str, None] = 'e8118d5f0e52'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('report_details', sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False), schema='biport_dev')
    op.add_column('report_details', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False), schema='biport_dev')
    op.add_column('report_details', sa.Column('created_by', sa.UUID(), nullable=True), schema='biport_dev')
    op.add_column('report_details', sa.Column('updated_by', sa.UUID(), nullable=True), schema='biport_dev')
    op.add_column('report_details', sa.Column('is_deleted', sa.Boolean(), server_default=sa.text('false'), nullable=True), schema='biport_dev')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    op.drop_column('report_details', 'is_deleted', schema='biport_dev')
    op.drop_column('report_details', 'updated_by', schema='biport_dev')
    op.drop_column('report_details', 'created_by', schema='biport_dev')
    op.drop_column('report_details', 'updated_at', schema='biport_dev')
    op.drop_column('report_details', 'created_at', schema='biport_dev')
    
    # ### end Alembic commands ###
