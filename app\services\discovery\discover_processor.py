from math import ceil
import json
import os
import aiofiles

from app.core.task import SiteDiscoveryService
from app.core.response import ServiceResponse
from app.core.exceptions import NotFoundError
from app.core.base_service import BaseService
from app.core.constants import FILE_TYPE, LOCAL_DIR, CLOUD_SITE_DISCOVERY_DIR, BA<PERSON><PERSON>GROUND_TASK
from app.core.config import S3Config, logger
from app.models_old.cloud_server import CloudServerDetailsManager
from app.models_old.server import ServerDetailsManager, ServerDetails
from app.core.enums import ServerType, DiscoverSiteStatus
from app.models_old.site_discovery import SiteDiscoveryManager
from app.schemas import DiscoverServerRequest
from app.services.discovery.discover_service import TableauClient, DiscoverService
from app.core.session import scoped_context
from app.models.tableau_server import TableauServerDetail 
from uuid import UUID

class DiscoverServerService(BaseService):
    """Handles the logic for discovering Tableau servers."""

    def __init__(self):
        self.server_details_manager = ServerDetailsManager
        self.cloud_server_details_manager = CloudServerDetailsManager
        self.site_discovery_manager = SiteDiscoveryManager()
        self.background_task = os.getenv(BACKGROUND_TASK, False)
        self.s3 = S3Config()

    async def _discover_cloud_server(self, server: ServerDetails) -> list[dict]:
        """Discovers a cloud Tableau server."""

        server_details = self.cloud_server_details_manager.get_cloud_server_by_id(server.id)
        if not server_details:
            logger.error(f"No cloud server details found for server_id={server.id}")
            raise NotFoundError("No cloud server details found.")


        if self.background_task:
            cloud_servers_ids = [server.id for server in server_details]

            cloud_sites_raw = self.site_discovery_manager.get_site_discovery_by_server_type_ids(server.id, cloud_servers_ids)
            if not cloud_sites_raw:
                err = f"No cloud sites found for server_id={server.id}"
                logger.error(err)
                raise NotFoundError(err)

            cloud_sites = [status for (status,) in cloud_sites_raw]

            completed_count = cloud_sites.count(DiscoverSiteStatus.COMPLETED)
            failed_count = cloud_sites.count(DiscoverSiteStatus.FAILED)
            total_sites = len(cloud_sites)
            pending_count = total_sites - completed_count - failed_count

            data = []

            if completed_count:
                local_file_path = os.path.join(LOCAL_DIR, f"{server.id}.{FILE_TYPE}")
                s3_object_path = f"{CLOUD_SITE_DISCOVERY_DIR}{server.id}.{FILE_TYPE}"
                if await self.s3.check_file_exists(s3_object_path):
                    await self.s3.download_file(s3_object_path, local_file_path)
                    async with aiofiles.open(local_file_path, mode="r") as f:
                        content = await f.read()
                    data = json.loads(content)
                    if os.path.exists(local_file_path):
                        os.remove(local_file_path)

            return {
                "data": data,
                "total": total_sites,
                "completed": completed_count,
                "pending": pending_count,
                "status": "In-Progress" if pending_count else "COMPLETED",
            }
        else:
            data = []
            for server_detail in server_details:
                logger.info(f"Fetching site data for PAT user: {server_detail.pat_name}")

                service = SiteDiscoveryService(
                    server_url=server.server_url,
                    server_id=server.id,
                    server_type_id=server_detail.id,
                    pat_name=server_detail.pat_name,
                    pat_secret=server_detail.pat_secret
                )

                site_data = await service._discover_site()

                logger.info(f"Site data fetched for site: {site_data.get('site_name')}")
                site_usage_sum = 0
                for project in site_data.get("projects", []):
                    client = TableauClient()
                    client.calculate_usage(project)
                    site_usage_sum += project.get("usage", 0)
                site_data["usage"] = site_usage_sum
                data.append(site_data)

            logger.info(f"Completed cloud server discovery for server_id={server.id}")
            return {
                "data": data,
                "total": len(data),
                "completed": len(data),
                "pending": 0,
                "status": "COMPLETED",
            }

    async def _discover_on_premise_server(self, server: ServerDetails) -> ServiceResponse:
        """Discovers an on-premise Tableau server."""
        logger.info(f"Starting on-premise server discovery for server_id={server.id}")

        server_details = self.server_details_manager.get_server_by_id(server.id)
        if not server_details:
            logger.error(f"No on-premise server details found for server_id={server.id}")
            raise NotFoundError("No on-premise server details found.")

        logger.info(f"Completed on-premise server discovery for server_id={server.id}")
        return ServiceResponse.success(data=server_details)

    async def _discover_server(self, request: DiscoverServerRequest, page: int, page_size: int) -> ServiceResponse:
        """Discovers a Tableau server."""
        logger.info(f"Initiating server discovery for request: {request}")

        server = self.server_details_manager.get_server_by_id(request.server_id)
        if server is None:
            logger.error(f"Server with ID {request.server_id} not found.")
            raise NotFoundError("Server not found.")

        logger.debug(f"Server type identified as: {server.server_type}")
        data = await self._discover_cloud_server(server) if server.server_type == ServerType.CLOUD.value else []
        total_sites = data.get("total", 0)
        offset = (page - 1) * page_size
        paginated_data = data.get("data", [])[offset: offset + page_size]

        response_data = {
            "content": paginated_data,
            "total": total_sites,
            "page": page,
            "page_size": len(paginated_data),
            "total_pages": ceil(total_sites / page_size) if page_size > 0 else 0,
            "completed": data.get("completed", 0),
            "pending": data.get("pending", 0),
            "status": data.get("status"),
        }


        return ServiceResponse.success(response_data)
    
class DiscoverProcessor:
    @staticmethod
    def process_get_all_servers(organization_id: UUID, page: int = 1, page_size: int = 10) -> ServiceResponse:
        """
        Process the request to get all Tableau servers for an organization using DiscoverService"""
        return DiscoverService.execute(DiscoverService()._get_all_servers,organization_id, page, page_size)

    @staticmethod
    def process_get_all_sites(server_id: UUID, page: int = 1, page_size: int = 10) -> ServiceResponse:
        return DiscoverService.execute(DiscoverService()._get_all_sites,server_id,page,page_size)

    @staticmethod
    def process_get_all_managers(organization_id: UUID,page: int,page_size: int=10) -> ServiceResponse:
        return DiscoverService.execute(DiscoverService()._get_all_managers,organization_id,page,page_size)
    
    @staticmethod
    def process_get_all_developers(organization_id: UUID,manager_id: UUID,page: int,page_size: int) -> ServiceResponse:
        return DiscoverService.execute( DiscoverService()._get_all_developers,organization_id, manager_id, page, page_size)

    @staticmethod
    def process_get_all_root_projects(page: int, page_size: int) -> ServiceResponse:
        return DiscoverService.execute(DiscoverService()._get_all_root_projects, page, page_size)
    
    @staticmethod
    def process_update_assign_to(project_id: UUID, user_id: UUID) -> ServiceResponse:
        return DiscoverService.execute(DiscoverService()._update_project_assigned_to, project_id, user_id)
   
    @staticmethod
    def process_get_all_projects_by_site(site_id: UUID) -> ServiceResponse:
        return DiscoverService.execute(DiscoverService()._get_all_projects_by_site,site_id)

    @staticmethod
    def process_get_projects_by_parent(project_id: UUID) -> ServiceResponse:
     return DiscoverService.execute(DiscoverService()._get_projects_by_parent_id, project_id)

    @staticmethod
    def process_get_all_developers_by_orgid(organization_id: UUID, page: int, page_size: int = 10) -> ServiceResponse:
        return DiscoverService.execute(
            DiscoverService()._get_all_developers_by_orgid,
            organization_id,
            page,
            page_size
        )
