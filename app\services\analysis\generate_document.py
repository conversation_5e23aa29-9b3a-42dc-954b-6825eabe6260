import json
import os
import re
from typing import Dict, Any, List
from docx import Document
from docx.shared import Pt, RGBColor
from docx.oxml.ns import qn
from docx.oxml import OxmlElement

from .calculations import get_final_formula
from app.core import logger

from  app.core.enums import (
    GeneralKeys as GS,
    Datasource as DS,
    WorkSheet as WS
)

from app.core.regex_enums import Regex as RE


def apply_formatting(paragraph, bold=False, font_size=11):
    """Apply Arial font, bold formatting, and font size to the first run in a paragraph."""
    if paragraph.runs:
        run = paragraph.runs[0]
        run.font.name = 'Arial'
        r = run._element
        r.rPr.rFonts.set(qn('w:eastAsia'), 'Arial')
        run.font.size = Pt(font_size)
        if bold:
            run.bold = True

def add_custom_heading(doc, text, font_size=16, bold=True, color=RGBColor(18, 69, 135)):
    """
    Add a custom heading with specified font size, boldness, and color.
    
    :param doc: The Word document object.
    :param text: The heading text.
    :param font_size: Font size for the heading.
    :param bold: Whether the text should be bold.
    :param color: RGBColor object for text color (default is blue).
    """
    paragraph = doc.add_paragraph(text)
    run = paragraph.runs[0]
    run.font.size = Pt(font_size)  # Set custom font size
    run.bold = bold               # Set bold text
    run.font.color.rgb = color    # Set text color to blue
    paragraph.style = doc.styles['Normal']  # Use Normal style to avoid default heading formatting
    return paragraph

def set_table_borders(table):
    """
    Set thicker borders for the table outline and thinner borders for inner rows.
    """
    tbl = table._element  # Access the table element
    tbl_pr = tbl.xpath("w:tblPr")[0]  # Access table properties

    # Create table border XML
    tbl_borders = OxmlElement('w:tblBorders')

    # Define outer border styles
    for border_name in ['top', 'left', 'bottom', 'right']:
        border = OxmlElement(f'w:{border_name}')
        border.set(qn('w:val'), 'single')  # Single line border
        border.set(qn('w:sz'), '13')  # Thicker border size for outline
        border.set(qn('w:space'), '0')
        border.set(qn('w:color'), '000000')  # Black color
        tbl_borders.append(border)

    # Define inner border styles (thin)
    for border_name in ['insideH', 'insideV']:
        border = OxmlElement(f'w:{border_name}')
        border.set(qn('w:val'), 'single')  # Single line border
        border.set(qn('w:sz'), '0')  # Thinner border size for inner rows
        border.set(qn('w:space'), '0')
        border.set(qn('w:color'), '36454F')  # Black color
        tbl_borders.append(border)

    tbl_pr.append(tbl_borders)

def set_header_bottom_border(cell):
    """
    Add a thicker border to the bottom of a header cell.
    """
    tc = cell._element
    tc_pr = tc.xpath("w:tcPr")[0] if tc.xpath("w:tcPr") else OxmlElement('w:tcPr')
    tc.append(tc_pr)

    tc_borders = tc_pr.xpath("w:tcBorders")[0] if tc_pr.xpath("w:tcBorders") else OxmlElement('w:tcBorders')
    tc_pr.append(tc_borders)

    bottom_border = OxmlElement('w:bottom')
    bottom_border.set(qn('w:val'), 'single')  # Single line
    bottom_border.set(qn('w:sz'), '12')  # Thicker border size for header bottom
    bottom_border.set(qn('w:space'), '0')
    bottom_border.set(qn('w:color'), '000000')  # Black color
    tc_borders.append(bottom_border)

def split_column_data(column):
    if not column:
        return None

    pattern = r"^\[[^:]+:[^:]+(:[^:]+)?\]$"
    if not bool(re.match(pattern, column)):
        return column
    column_data = column.strip('[]').split(':')

    if not column_data:
        return None

    if len(column_data) == 1:
        return f" column: {column_data[0]}"

    if len(column_data) == 2:
        if column_data[0] == '':
            return " column: union of multiple measures"
        else:
            return f" aggregate function: {column_data[0]}\n column: {column_data[1]}"

    if len(column_data) == 3:
        key_type_map = {
            'nk': 'nominal key',
            'qk': 'quantitative key',
            'ok': 'ordinal key'
        }
        key_type = key_type_map.get(column_data[2], column_data[2])
        return f" aggregate function: {column_data[0]}\n column: {column_data[1]}\n key_type: {key_type}"
      
def extract_summary(datasource_json, output_path, worksheet_json, dashboard_json, doc, folder_name, user):
    from app.models_old.user_reports import UserReportsManager

    num_datasources = datasource_json.get(DS.Datasources.value, {}).get("datasource_count", 0)
    
    if isinstance(worksheet_json, dict):
        num_worksheets = len(worksheet_json.get(GS.WorkSheets.value, []))
    elif isinstance(worksheet_json, list):
        num_worksheets = len(worksheet_json)
    else:
        num_worksheets = 0
    
    num_dashboards = len(dashboard_json.get(GS.Dashboards.value, []))
    
    calculations = datasource_json.get(DS.Datasources.value, {}).get(DS.CALC_IN_DS.value, [])
    calculated_fields = [calc for calc in calculations if DS.CAPTION.value in calc]
    num_calculated_fields = len(calculated_fields)
    
    parameters = []
    if num_dashboards > 0:
        datasource_dependencies = dashboard_json.get(GS.Dashboards.value, [])[0].get("datasource-dependencies", [])
        if datasource_dependencies:
            parameters = datasource_dependencies[0].get(GS.COLUMNS.value, [])
    num_parameters = len(parameters)
    
    has_filters = "Yes" if len(datasource_json.get(DS.Datasources.value, {}).get(DS.FILTERS.value, [])) > 0 else "No"
    
    doc.add_paragraph(f"No. of DataSources: {num_datasources}")
    doc.add_paragraph(f"No. of Worksheets: {num_worksheets}")
    doc.add_paragraph(f"No. of Dashboards: {num_dashboards}")
    doc.add_paragraph(f"No. of Calculations: {num_calculated_fields}")
    doc.add_paragraph(f"No. of Parameters: {num_parameters}")
    doc.add_paragraph(f"Has Filters: {has_filters}")


    UserReportsManager.add_or_update_user_reports_details(
        num_datasources=num_datasources,
        num_worksheets=num_worksheets,
        num_dashboards=num_dashboards,
        num_calculated_fields=num_calculated_fields,
        user=user
    )

def table_details(table_details: List[Dict[str, Any]], doc: Any) -> None:
    """
    Adds table details (name, columns, data types) to the document.

    Args:
        table_details: List of dictionaries containing table info.
        doc: Document object to add content to.
    """
    title = "Table Details"
    logger.info(f"Adding section table: {title}")
    heading = doc.add_paragraph(title)
    apply_formatting(heading, bold=True, font_size=14)

    for table_info in table_details:
        table_name = table_info.get(GS.NAME.value, GS.UNNAMED_TABLE.value)
        columns_data = table_info.get(GS.Columns_and_Datatypes.value, [])

        logger.info(f"Processing table: {table_name}, Total Columns: {len(columns_data)}")

        columns = []

        for col in columns_data:
            if GS.Columns_and_Datatypes.value in col:
                for inner_col in col[GS.Columns_and_Datatypes.value]:
                    columns.append({
                        GS.NAME.value: inner_col.get(GS.NAME.value, GS.UNNAMED_COLUMN.value),
                        GS.DATATYPE.value: inner_col.get(GS.DATATYPE.value, GS.UNKNOWN.value)
                    })
            else:
                columns.append({
                    GS.NAME.value: col.get(GS.NAME.value, GS.UNNMAED_COLUMN.value),
                    GS.DATATYPE.value: col.get(GS.DATATYPE.value, GS.UNKNOWN.value)
                })

        if not columns:
            logger.warning(f"No column data found for table: {table_name}")
            columns = [{GS.NAME.value: "Placeholder Column", GS.DATATYPE.value: "string"}]

        schema = GS.DEFAULT_Schema.value
        pattern = r'\[(.*?)\]\.\[(.*?)\]'  
        match = re.search(pattern, table_name)
        
        if match:
            schema = match.group(1)  
            table_name = match.group(2)  
            logger.info(f"Extracted Schema: {schema}, Table Name: {table_name}")
        
        if schema == "Extract":
            schema = GS.DEFAULT_Schema.value

        heading = doc.add_paragraph(f"\nSchema: {schema}")
        apply_formatting(heading, font_size=12, bold=True)
        
        paragraph = doc.add_paragraph(f"Table: {table_name}")
        apply_formatting(paragraph, font_size=12, bold=True)     
    
        table = doc.add_table(rows=1, cols=2)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Column Name'
        hdr_cells[0].paragraphs[0].runs[0].bold = True
        hdr_cells[1].text = 'Data Type'
        hdr_cells[1].paragraphs[0].runs[0].bold = True

        set_header_bottom_border(hdr_cells[0])
        set_header_bottom_border(hdr_cells[1])

        for col in columns:
            col_name = col.get(GS.NAME.value, GS.UNNAMED_COLUMN.value)  
            col_type = col.get(GS.DATATYPE.value, GS.UNKNOWN.value)  
            row_cells = table.add_row().cells
            row_cells[0].text = col_name
            row_cells[1].text = col_type

        set_table_borders(table)

def semantic_mapping(mapping_dict: Dict[str, str], doc: Any) -> None:
    """
    Adds a semantic mapping section to the document.

    Args:
        mapping_dict: Dict of mapping_value to table_column string pairs.
        doc: Document object to add content to.
    """
    title = "Semantic Mapping"
    logger.info(f"Adding section: {title}")
    heading = doc.add_paragraph("\n\n" + title)
    apply_formatting(heading, bold=True, font_size=14)

    table_mappings: Dict[str, List[Dict[str, str]]] = {}

    for mapping_value, table_column in mapping_dict.items():
        if GS.TABLE_COLUMN_SEPARATOR.value not in table_column:
            logger.warning(f"Skipping malformed table_column value: {table_column}")
            continue

        try:
            split_result = table_column.split(GS.TABLE_COLUMN_SEPARATOR.value, 1)
            if len(split_result) != 2:
                logger.warning(f"Malformed table_column format: {table_column}")
                continue

            table_name_raw, column_name_raw = split_result

            clean_table_name = table_name_raw.strip('[]')
            clean_column_name = column_name_raw.strip('[]')
            clean_mapping_value = mapping_value.strip('[]')

        except ValueError as e:
            logger.error(f"Error parsing mapping '{mapping_value}': {e}")
            continue

        if clean_table_name not in table_mappings:
            table_mappings[clean_table_name] = []

        table_mappings[clean_table_name].append({
            GS.COLUMN_NAME_KEY.value: clean_column_name,
            GS.MAPPING_VALUE_KEY.value: clean_mapping_value
        })

    for table_name, columns in table_mappings.items():
        paragraph = doc.add_paragraph(f"\nTable: {table_name}")
        apply_formatting(paragraph, font_size=12, bold=True)

        table = doc.add_table(rows=1, cols=2)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = "Column Name"
        hdr_cells[0].paragraphs[0].runs[0].bold = True
        hdr_cells[1].text = "Mapping Value"
        hdr_cells[1].paragraphs[0].runs[0].bold = True

        set_header_bottom_border(hdr_cells[0])
        set_header_bottom_border(hdr_cells[1])

        for mapping in columns:
            row_cells = table.add_row().cells
            row_cells[0].text = mapping[GS.COLUMN_NAME_KEY.value]
            row_cells[1].text = mapping[GS.MAPPING_VALUE_KEY.value]

        set_table_borders(table)


def calculated_fields(calculations, doc, calculations_json = []):
    title = "Expressions/Measures"
    logger.info(f"Adding section table: {title}")

    heading = doc.add_paragraph("\n" + title)
    apply_formatting(heading, bold=True, font_size=14)

    table = doc.add_table(rows=1, cols=2)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Name'
    hdr_cells[0].paragraphs[0].runs[0].bold = True
    hdr_cells[1].text = 'Formula'
    hdr_cells[1].paragraphs[0].runs[0].bold = True

    set_header_bottom_border(hdr_cells[0])
    set_header_bottom_border(hdr_cells[1])

    for calculation in calculations:
            raw_formula = calculation.get('formula', 'No Formula')
            if not raw_formula:
                resolved_formula = 'No Formula'
            else:
                resolved_formula = get_final_formula(raw_formula, calculations_json)

            row_cells = table.add_row().cells
            caption = calculation.get('caption', 'Unnamed Calculation')

            if not caption:
                caption = 'Unnamed Calculation'

            row_cells[0].text = caption
            row_cells[1].text = resolved_formula if resolved_formula else 'No Formula'
    
    set_table_borders(table)

def filter_details(filters, doc):
    title = "Filters"
    logger.info(f"Adding section table: {title}")

    heading = doc.add_paragraph("\n" + title)
    apply_formatting(heading, bold=True, font_size=14)

    table = doc.add_table(rows=1, cols=4)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Type'
    hdr_cells[0].paragraphs[0].runs[0].bold = True
    hdr_cells[1].text = 'Column'
    hdr_cells[1].paragraphs[0].runs[0].bold = True
    hdr_cells[2].text = 'Group Function'
    hdr_cells[2].paragraphs[0].runs[0].bold = True
    hdr_cells[3].text = 'Group Level'
    hdr_cells[3].paragraphs[0].runs[0].bold = True

    set_header_bottom_border(hdr_cells[0])
    set_header_bottom_border(hdr_cells[1])
    set_header_bottom_border(hdr_cells[2])
    set_header_bottom_border(hdr_cells[3])
    
    for filter_data in filters:
        row_cells = table.add_row().cells
        row_cells[0].text = filter_data.get('class', 'None')

        column = filter_data.get('column', 'None')
        splitted_column = split_column_data(column)
        row_cells[1].text = splitted_column

        groupfilter = filter_data.get('groupfilter', {})
        row_cells[2].text = groupfilter.get('function', 'None')

        level = groupfilter.get('level', 'None')
        splitted_level = split_column_data(level)
        row_cells[3].text = splitted_level
    
    set_table_borders(table)

def column_details(columns, doc):
    title = "Column Details"
    logger.info(f"Adding section table: {title}")

    heading = doc.add_paragraph("\n" + title)
    apply_formatting(heading, bold=True, font_size=14)

    table = doc.add_table(rows=1, cols=4)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Column'
    hdr_cells[0].paragraphs[0].runs[0].bold = True
    hdr_cells[1].text = 'Aggregate Function'
    hdr_cells[1].paragraphs[0].runs[0].bold = True
    hdr_cells[2].text = 'Pivot'
    hdr_cells[2].paragraphs[0].runs[0].bold = True
    hdr_cells[3].text = 'Key type'
    hdr_cells[3].paragraphs[0].runs[0].bold = True

    set_header_bottom_border(hdr_cells[0])
    set_header_bottom_border(hdr_cells[1])
    set_header_bottom_border(hdr_cells[2])
    set_header_bottom_border(hdr_cells[3])
    
    for column_data in columns:
        row_cells = table.add_row().cells

        pattern = "^\[[^\]]+\]\.\[[^\]]+\]$"
        column = column_data.get('column', 'None')
        if re.match(pattern, column):
            column = column.split('.')[1].strip('[]')
        else:
            column = column.strip('[]')

        row_cells[0].text = column
        row_cells[1].text = column_data.get('derivation', 'None')
        row_cells[2].text = column_data.get('pivot', 'None')
        row_cells[3].text = column_data.get('type', 'None')
    
    set_table_borders(table)

def slicer_details(slicers, doc):
    title = "Slicers"
    logger.info(f"Adding section table: {title}")

    heading = doc.add_paragraph("\n" + title)
    apply_formatting(heading, bold=True, font_size=14)

    table = doc.add_table(rows=1, cols=3)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = 'Column'
    hdr_cells[0].paragraphs[0].runs[0].bold = True
    hdr_cells[1].text = 'Aggregate Function'
    hdr_cells[1].paragraphs[0].runs[0].bold = True
    hdr_cells[2].text = 'Key type'
    hdr_cells[2].paragraphs[0].runs[0].bold = True

    set_header_bottom_border(hdr_cells[0])
    set_header_bottom_border(hdr_cells[1])
    set_header_bottom_border(hdr_cells[2])
    
    for slicer in slicers:
        slicer_data = slicer.strip('[]').split(':')
        row_cells = table.add_row().cells
        if len(slicer_data) == 1:
            row_cells[0].text = slicer_data[0]
            row_cells[1].text = 'None'
            row_cells[2].text = 'None'

        if len(slicer_data) == 2:
            row_cells[0].text = slicer_data[1]
            row_cells[1].text = slicer_data[0]
            row_cells[2].text = 'None'
        if len(slicer_data) == 3:
            key_type_map = {
                'nk': 'nominal key',
                'qk': 'quantitative key',
                'ok': 'ordinal key'
            }

            row_cells[0].text = slicer_data[1]
            row_cells[1].text = slicer_data[0]
            row_cells[2].text = key_type_map.get(slicer_data[2], slicer_data[2])

    set_table_borders(table)

def add_section_table(doc, title, content, is_panes_rows_cols=False, is_zones=False, is_columns=False, is_slicers=False):
    """Add a table with a title for a specific section of the JSON content."""
    logger.info(f"Adding section table: {title}")
    
    # Add the title to the document
    heading = doc.add_paragraph("\n" + title)
    apply_formatting(heading, bold=True, font_size=14)
    
    if is_columns:
        # Combine all columns into a single table and exclude role, type, param-domain-type
        headers = ['Caption', 'Datatype', 'Name', 'Value', 'Calculation']
        table = doc.add_table(rows=1, cols=len(headers))
        hdr_cells = table.rows[0].cells
        
        for i, header in enumerate(headers):
            hdr_cells[i].text = header.capitalize()
            hdr_cells[i].paragraphs[0].runs[0].bold = True
            set_header_bottom_border(hdr_cells[i])

        for column in content:
            row_cells = table.add_row().cells
            row_cells[0].text = column.get('caption', 'None')
            row_cells[1].text = column.get('datatype', 'None')
            row_cells[2].text = column.get('name', 'None').strip('[]')
            row_cells[3].text = column.get('value', 'None')
            calculation = column.get('calculation', {})
            row_cells[4].text = calculation.get('formula', 'None')
    
    elif is_panes_rows_cols:
        # Create a table with 2 columns: Key and Value
        table = doc.add_table(rows=1, cols=2)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Key'
        hdr_cells[0].paragraphs[0].runs[0].bold = True
        hdr_cells[1].text = 'Value'
        hdr_cells[1].paragraphs[0].runs[0].bold = True

        set_header_bottom_border(hdr_cells[0])
        set_header_bottom_border(hdr_cells[1])
        
        for item in content:
            for key, value in item.items():
                if key.lower() == 'style':
                    continue  # Skip the 'style' key
                if isinstance(value, dict) or isinstance(value, list):
                    value = str(value)  # Convert dict or list to string
                row_cells = table.add_row().cells
                row_cells[0].text = key.capitalize()
                row_cells[1].text = value

    elif is_zones:
        # Create a table with 7 columns: h, Name, Param, Type, Width, X, Y
        headers = ['Height', 'Name', 'Param', 'Type', 'Width', 'X-Coordinate', 'Y-Coordinate']
        table = doc.add_table(rows=1, cols=len(headers))
        hdr_cells = table.rows[0].cells
        
        for i, header in enumerate(headers):
            hdr_cells[i].text = header.capitalize()
            hdr_cells[i].paragraphs[0].runs[0].bold = True
            set_header_bottom_border(hdr_cells[i])
        
        for zone in content:
            row_cells = table.add_row().cells
            row_cells[0].text = zone.get('h', 'None')
            row_cells[1].text = zone.get('name', 'None')
            param = zone.get('param', 'None')
            if param:
                param = split_column_data(param)
            else:
                param = "None"
            # print(param)
            row_cells[2].text = param
            row_cells[3].text = zone.get('type-v2', 'None')
            row_cells[4].text = zone.get('w', 'None')
            row_cells[5].text = zone.get('x', 'None')
            row_cells[6].text = zone.get('y', 'None')
    
    elif is_slicers:
        # Create a table for slicers
        table = doc.add_table(rows=1, cols=2)
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Slicer Index'
        hdr_cells[0].paragraphs[0].runs[0].bold = True
        hdr_cells[1].text = 'Slicer Value'
        hdr_cells[1].paragraphs[0].runs[0].bold = True
        
        for i, slicer_item in enumerate(content):
            row_cells = table.add_row().cells
            row_cells[0].text = f'Slicer {i+1}'
            row_cells[1].text = str(slicer_item)
    
    else:
        # Handle the case where content is a list (in general cases)
        if isinstance(content, list):
            # Assume it's a list of dictionaries
            if content and isinstance(content[0], dict):
                headers = list(content[0].keys())
                table = doc.add_table(rows=1, cols=len(headers))
                hdr_cells = table.rows[0].cells
                
                for i, header in enumerate(headers):
                    hdr_cells[i].text = header.capitalize()
                    hdr_cells[i].paragraphs[0].runs[0].bold = True
                
                for item in content:
                    row_cells = table.add_row().cells
                    for i, header in enumerate(headers):
                        row_cells[i].text = str(item.get(header, 'None'))
            else:
                # Fallback for other list formats
                table = doc.add_table(rows=1, cols=1)
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'Content'
                
                for item in content:
                    row_cells = table.add_row().cells
                    row_cells[0].text = str(item)
                
        elif isinstance(content, dict):
            # Handle the case where content is a dictionary
            table = doc.add_table(rows=0, cols=2)
            
            for key, value in content.items():
                if key.lower() in ["style", "role", "type", "param-domain-type", "fixed-size", "id", "is-fixed", "pane-specification-id"]:
                    continue  # Skip unwanted details
                
                row_cells = table.add_row().cells
                row_cells[0].text = key.capitalize()
                row_cells[1].text = str(value) if value else "Empty"
                
                apply_formatting(row_cells[0].paragraphs[0], bold=False)
                apply_formatting(row_cells[1].paragraphs[0], bold=False)
    set_table_borders(table)

def process_datasource(doc, datasource, calculations_json):
    """Process the 'datasource' section."""
    logger.info("Processing datasources")
    if 'Tables and Columns' in datasource:
        tables = datasource['Tables and Columns']
        if isinstance(tables, list):
            table_details(tables, doc)
    
    if 'Semantic Mapping' in datasource and datasource['Semantic Mapping']:
        semantic_mapping(datasource['Semantic Mapping'], doc)

    if 'Joins' in datasource and datasource['Joins']:
        heading = doc.add_paragraph("\nJoins")
        apply_formatting(heading, font_size=14, bold=True)

        for i, join_value in enumerate(datasource['Joins'].values(), start=1):
            bullet = doc.add_paragraph()
            bullet.add_run(f"{i}. {join_value}")
            bullet.style = doc.styles['Normal']
    
    if 'Calculations' in datasource and datasource[GS.CALCULATIONS.value]:
        calculations = datasource[GS.CALCULATIONS.value]
        if isinstance(calculations, list):
            calculated_fields(calculations, doc, calculations_json)
    
    if 'Filters' in datasource and datasource['Filters']:
        filters = datasource['Filters']
        if isinstance(filters, list):
            filter_details(filters, doc)

def process_worksheet(doc, worksheet, charts_json):
    """Process the 'worksheet' section."""
    logger.info(f"Processing worksheet: {worksheet.get(GS.NAME.value)}")
    paragraph = doc.add_paragraph(f"\nWorksheet: {worksheet.get(GS.NAME.value)}")
    apply_formatting(paragraph, bold=True, font_size=14)
    for chart in charts_json:
        if chart.get(GS.NAME.value) == worksheet.get(GS.NAME.value):
            chart_type = chart.get("chart_type", GS.UNKNOWN.value)
            break
    paragraph = doc.add_paragraph(f"Visual Type: {chart_type}")
    apply_formatting(paragraph, bold=True, font_size=12)

    rows = ", ".join(worksheet["rows"])
    cols = ", ".join(worksheet["cols"])
    if not rows:
        rows = "None"
    if not cols:
        cols = "None"
    paragraph = doc.add_paragraph(f"Fields used in rows: {rows}\nFields used in cols: {cols}")

    if "column-instances" in worksheet:
        column_instances = worksheet[GS.COLUMN_INSTANCES.value]
        if isinstance(column_instances, list): 
            column_details(column_instances, doc)
    
    if "filters" in worksheet:
        filters = worksheet[GS.FILTERS.value]
        if isinstance(filters, list):
            filter_details(filters, doc)
    
    if "slices" in worksheet:
        slicers = worksheet[GS.SLICES.value]
        if isinstance(slicers, list):
            if len(slicers)>0:
                slicer_details(slicers, doc)

def process_dashboard(doc, dashboard):
    """Process the 'dashboard' section."""
    logger.info(f"Processing dashboard: {dashboard.get(GS.NAME.value)}")
    paragraph = doc.add_paragraph(f"\nDashboard: {dashboard.get(GS.NAME.value)}")
    apply_formatting(paragraph, bold=True, font_size=12)

    # Process datasources
    if "datasource-dependencies" in dashboard:
        for dependency in dashboard["datasource-dependencies"]:
            if isinstance(dependency, dict) and GS.COLUMNS.value in dependency:
                columns = dependency.pop(GS.COLUMNS.value)
                add_section_table(doc, "Column_Details", columns, is_columns=True)
            else:
                add_section_table(doc, "Datasource Dependency", dependency)
    
    # Process zones
    if "zones" in dashboard:
        zones = dashboard[GS.ZONES.value]
        if isinstance(zones, list):
            add_section_table(doc, "Visual Dimensions", zones, is_zones=True)
    
    # Process filters
    if "filters" in dashboard:
        filters = dashboard[GS.FILTERS.value]
        if isinstance(filters, list):
            filter_details(filters, doc)

def generate_report_from_json(datasource_json, worksheet_json, dashboard_json, output_path,folder_name, calculations_json, charts_json, user, workbook_name):
    doc = Document()
    doc.add_heading(f"{workbook_name} - Analysis Report", 0)

    extract_summary(datasource_json, output_path, worksheet_json, dashboard_json, doc, folder_name, user)

    # Add a heading for datasources before processing them
    if datasource_json:
        add_custom_heading(doc, '\nDatasources', font_size=16, bold=True)
        for name, details in datasource_json.items():
            process_datasource(doc, details, calculations_json)
    
    # Process worksheets
    if worksheet_json and GS.WorkSheets.value in worksheet_json:
        add_custom_heading(doc, '\nWorksheets', font_size=16, bold=True)
        for worksheet in worksheet_json[GS.WorkSheets.value]:
            process_worksheet(doc, worksheet, charts_json)
    
    # Process dashboards
    if dashboard_json and GS.DASHBOARDS.value in dashboard_json:
        add_custom_heading(doc, '\nDashboards', font_size=16, bold=True)
        for dashboard in dashboard_json[GS.DASHBOARDS.value]:
            process_dashboard(doc, dashboard)

    # if not os.path.exists(os.path.dirname(output_path)):
    #     logger.error(f"Directory does NOT exist: {os.path.dirname(output_path)}")
    # else:
    #     logger.info(f"Directory exists: {os.path.dirname(output_path)}")

    # try:
    #     doc.save(output_path)
    #     logger.info(f"Report successfully saved to {output_path}")

    # except Exception as e:
    #     logger.error(f"Error while saving report: {e}", exc_info=True)

    try:
        output_path = os.path.normpath(output_path)  # Clean slashes
        output_dir = os.path.dirname(output_path)

        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"Created directory: {output_dir}")
        else:
            logger.info(f"Directory already exists: {output_dir}")

        doc.save(output_path)
        logger.info(f"Report successfully saved to {output_path}")

    except Exception as e:
        logger.error(f"Error while saving report: {e}", exc_info=True)
    
def generate_reports_from_json(input_dir, user, workbook_name):
    logger.info(f"Generating reports from JSON files in {input_dir}")

    for folder_name in os.listdir(input_dir):
        folder_path = os.path.join(input_dir, folder_name)
        
        if os.path.isdir(folder_path):
            datasource_path = os.path.join(folder_path, "datasources.json")
            worksheet_path = os.path.join(folder_path, "worksheets.json")
            dashboard_path = os.path.join(folder_path, "dashboards.json")
            calculations_path = os.path.join(folder_path, "calculations.json")
            charts_path = os.path.join(folder_path, "chart_types.json") 

            if all(os.path.exists(p) for p in [datasource_path, worksheet_path, dashboard_path, charts_path]):
                with open(datasource_path, 'r') as ds_file, open(worksheet_path, 'r') as ws_file,open(dashboard_path, 'r') as db_file,open(charts_path, 'r') as charts_file,open(calculations_path, 'r') as calc_file:

                    datasource_json = json.load(ds_file)
                    worksheet_json = json.load(ws_file)
                    dashboard_json = json.load(db_file)
                    calculations_json = json.load(calc_file)
                    charts_json_data = json.load(charts_file)

                # Since now it's per-file, just use the value directly
                charts = charts_json_data.get("chart_types", [])

                output_file_name = f"{workbook_name} - Analysis Report.docx"
                output_file_path = os.path.join(folder_path, output_file_name)
                os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

                generate_report_from_json(
                    datasource_json,
                    worksheet_json,
                    dashboard_json,
                    output_file_path,
                    folder_name,
                    calculations_json,
                    charts,
                    user,
                    workbook_name
                )
                logger.info(f"Report generated for {folder_name}: {output_file_path}")
            else:
                logger.warning(f"Missing required JSON files in {folder_path}. Skipping this folder.")
