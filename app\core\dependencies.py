import json
from app.core.enums import Role<PERSON>num
from app.models.roles import RoleManager
from app.models.users import User, UserManager
from app.core import Authentication<PERSON><PERSON><PERSON>, AuthorizationError, BLOCKED_EMAILS, logger
from fastapi import Depends, Header, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.services.auth import decode_secure_jwt, decode_base


from app.core import scoped_context
from app.core.enums import RoleEnum
from app.models.users import User
from app.models.roles import RoleManager
from app.core.exceptions import AuthorizationError
from sqlalchemy.orm import joinedload

security = HTTPBearer(auto_error=True)

async def get_current_user(
    authorization: str = Header(..., alias="Authorization"),
    encoded_email: str = Header(..., alias="X-User-Email")
):
    try:
        logger.info(f"[get_current_user] Starting authentication")
        print(f"[DEBUG] get_current_user called")

        if not authorization.startswith("Bearer "):
            logger.error(f"[get_current_user] Invalid Authorization header format")
            print(f"[ERROR] Invalid Authorization header format")
            raise AuthorizationError("Invalid Authorization header")

        token = authorization.split(" ")[1]
        logger.info(f"[get_current_user] Token extracted, length: {len(token)}")
        print(f"[DEBUG] Token length: {len(token)}")

        email = decode_base(encoded_email)
        logger.info(f"[get_current_user] Email decoded: {email}")
        print(f"[DEBUG] Decoded email: {email}")

        jwt_result = decode_secure_jwt(token, email)
        logger.info(f"[get_current_user] JWT decoded successfully")
        print(f"[DEBUG] JWT decoded: {jwt_result}")

        payload = json.loads(jwt_result.get("sub"))
        if not payload:
            logger.error(f"[get_current_user] Invalid token payload")
            print(f"[ERROR] Invalid token payload")
            raise AuthenticationError("Invalid token")

        logger.info(f"[get_current_user] Payload extracted: {payload}")
        print(f"[DEBUG] Payload: {payload}")

        # Extract role from payload
        role_name = payload.get("role")
        if not role_name:
            logger.error(f"[get_current_user] Role not found in token")
            print(f"[ERROR] Role not found in token")
            raise AuthenticationError("Role not found in token")

        logger.info(f"[get_current_user] Role extracted: {role_name}")
        print(f"[DEBUG] Role: {role_name}")

        # Fetch user with role eagerly loaded
        user = UserManager.get_user_by_email(email, load_role=True)
        if not user:
            logger.error(f"[get_current_user] User not found: {email}")
            print(f"[ERROR] User not found: {email}")
            raise AuthenticationError("User not found")

        logger.info(f"[get_current_user] User found: {user.name}")
        print(f"[DEBUG] User found: {user.name}")

        # Attach role_name from login response
        user.role_name = role_name
        logger.info(f"[get_current_user] Authentication successful")
        print(f"[DEBUG] Authentication successful")
        return user
    except AuthenticationError as e:
        logger.error(f"[get_current_user] AuthenticationError: {e}")
        print(f"[ERROR] AuthenticationError: {e}")
        raise HTTPException(status_code=401, detail=str(e))
    except AuthorizationError as e:
        logger.error(f"[get_current_user] AuthorizationError: {e}")
        print(f"[ERROR] AuthorizationError: {e}")
        raise HTTPException(status_code=403, detail=str(e))
    except Exception as e:
        logger.error(f"[get_current_user] Unexpected error: {e}", exc_info=True)
        print(f"[ERROR] Unexpected error in get_current_user: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="Authentication failed")

async def get_current_new_user(
    authorization: str = Header(..., alias="Authorization"),
    encoded_email: str = Header(..., alias="X-User-Email")
):
    try:
        if not authorization.startswith("Bearer "):
            raise AuthorizationError("Invalid Authorization header")

        token = authorization.split(" ")[1]
        email = decode_base(encoded_email)
        payload = json.loads(decode_secure_jwt(token, email).get("sub"))
        if not payload:
            raise AuthenticationError("Invalid token")

        # Extract role from payload
        role_name = payload.get("role")
        if not role_name:
            raise AuthenticationError("Role not found in token")

        # Fetch user with role eagerly loaded
        user = UserManager.get_user_by_email(email, load_role=True)
        if not user:
            raise AuthenticationError("User not found")

        # Attach role_name from login response
        user.role_name = role_name
        return user
    except Exception as e:
        raise HTTPException(status_code=e.status_code, detail=str(e))

def get_verified_user_email(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    encoded_email: str = Header(..., alias="X-User-Email")
) -> str:
    token = credentials.credentials
    try:
        email = decode_base(encoded_email)
        payload = json.loads(decode_secure_jwt(token, email).get("sub"))

        if payload.get("email") != email:
            raise AuthenticationError("Token/email mismatch")

        return email
    except Exception as e:
        raise AuthenticationError(str(e))

async def check_blocked_email(request: Request):
    email = request.headers.get("X-User-Email") or request.query_params.get("email")
    if email and email in BLOCKED_EMAILS:
        logger.warning(f"Blocked email access attempt: {email}")
        raise AuthorizationError("Access denied. This email is blocked.")

async def check_if_admin(
    user: User = Depends(get_current_user)
):
    role_name = RoleManager.get_role_name(user.role_id)
    if role_name != RoleEnum.ADMIN:
        raise AuthorizationError("Only Admins are authorized to access this endpoint.")
    return user