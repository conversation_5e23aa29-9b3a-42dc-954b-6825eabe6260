import os
import logging
import aiofiles
from typing import Optional, List
from pydantic_settings import BaseSettings
from sqlalchemy import create_engine
import aioboto3
from openai import OpenAI
from dotenv import load_dotenv
from app.core.constants import HTTP_STATUS_INTERNAL_ERROR, MSG_S3_DOWNLOAD_FAILED, MSG_S3_FETCH_ERROR
from fastapi import HTTPEx<PERSON>, status
from pathlib import Path

# Load environment variables
load_dotenv()

def setup_logger(logger_name: str) -> logging.Logger:
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)

    # Clear existing handlers if the logger was already configured
    if logger.hasHandlers():
        logger.handlers.clear()

    # Console handler for logging to stdout
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(
        logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
        )
    )
    logger.addHandler(console_handler)

    return logger

logger = setup_logger("BIport-Log")

#for semantic_model V2 api
class PathConfig:
    powerbi_structure_path = 'demo_files/powerbi_structure'
    semantic_model_path = 'static/semantic_model_structure'
    dateTable_template_path = 'static/DateTemplate.tmdl'

class S3Config(BaseSettings):
    region_name: str = os.getenv("AWS_REGION")
    aws_access_key_id: str = os.getenv("AWS_ACCESS_KEY")
    aws_secret_access_key: str = os.getenv("AWS_SECRET_KEY")
    endpoint_url: str = os.getenv("AWS_ENDPOINT")
    bucket_name: str = os.getenv("AWS_S3_BUCKET_NAME")
 
    def get_s3_client(self):
        return aioboto3.Session().client(
            "s3",
            region_name=self.region_name,
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            endpoint_url=self.endpoint_url,
        )
                
    async def upload_to_s3(self, file_path: str, object_name: str) -> bool:
        try:
            async with self.get_s3_client() as s3:
                with open(file_path, "rb") as f:
                    await s3.upload_fileobj(f, self.bucket_name, object_name)
            return True
        except Exception as e:
            logger.error(f"Upload to S3 failed: {e}")
            return False

    async def check_file_exists(self, object_name: str) -> bool:
        """
        Checks whether an object exists in the S3 bucket.
        Returns True if it exists, otherwise False.
        """
        try:
            async with self.get_s3_client() as s3:
                # head_object will succeed if the object exists, or raise an exception if not.
                await s3.head_object(Bucket=self.bucket_name, Key=object_name)
            return True
        except Exception as e:
            return False

    async def copy_object(self, source_key: str, destination_key: str) -> bool:
        """
        Copies an object from source_key to destination_key within the same bucket.
        Returns True if successful, otherwise False.
        """
        try:
            async with self.get_s3_client() as s3:
                copy_source = {"Bucket": self.bucket_name, "Key": source_key}
                await s3.copy_object(Bucket=self.bucket_name, CopySource=copy_source, Key=destination_key)
            return True
        except Exception as e:
            logger.error(f"Copy object in S3 failed: {e}")
            return False

    async def delete_object(self, object_key: str) -> bool:
        """
        Deletes an object from the S3 bucket.
        Returns True if successful, otherwise False.
        """
        try:
            async with self.get_s3_client() as s3:
                await s3.delete_object(Bucket=self.bucket_name, Key=object_key)
            return True
        except Exception as e:
            logger.error(f"Delete object from S3 failed: {e}")
            return False

    async def download_file(self, object_name: str, file_path: str) -> None:
        """Downloads an object from S3 to a local file."""
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        async with self.get_s3_client() as s3:
            async with aiofiles.open(file_path, "wb") as f:
                await s3.download_fileobj(self.bucket_name, object_name, f)

    async def generate_presigned_url(self, object_key: str, expiration: int = 3600) -> Optional[str]:
        try:
            async with self.get_s3_client() as s3_client:
                return await s3_client.generate_presigned_url(
                    ClientMethod="get_object",
                    Params={"Bucket": self.bucket_name, "Key": object_key},
                    ExpiresIn=expiration,
                )
        except Exception as e:
            logger.error(f"Error generating presigned URL: {e}")
            return None
        
    @staticmethod
    def extract_s3_key(filepath: str, bucket_name: str) -> str:
        return filepath.replace(f"s3://{bucket_name}/", "")
    
    async def get_twb_files(self,
                            s3_input_path,
                            local_download_path):
            try:
                async with self.get_s3_client() as s3_client:
                        response = await s3_client.list_objects_v2(
                            Bucket = self.bucket_name, 
                            Prefix = s3_input_path.lstrip("/")
                        )
                        if "Contents" not in response:
                            raise HTTPException(
                                status_code = 400,
                                detail = "No files found in S3 input path",
                            )
                        twb_files_path = []
                        for obj in response["Contents"]:
                            file_key = obj["Key"]
                            file_name = os.path.basename(file_key)
                            local_file_path = os.path.join(local_download_path, file_name)
                            await s3_client.download_file(
                                                        self.bucket_name,
                                                        file_key,
                                                        local_file_path,
                                                        )
                            twb_files_path.append(local_file_path)
                        return twb_files_path
            except Exception as s3er:
                raise HTTPException(status_code = status.HTTP_500_INTERNAL_SERVER_ERROR,
                                    detail = f"Problem in fetching previous data: {str(s3er)}")

    async def download_twb_file_from_s3(self,
                            s3_key: str,
                            local_download_path: str) -> list[str]:
            """
            Download a .twb file from S3 to a local path.

            Parameters
            ----------
            s3_key : str
                The S3 object key of the .twb file to download.
            local_download_path : str
                The local directory where the file should be saved.

            Returns
            -------
            List containing the local file path of the downloaded file.
            """

            try:
                local_dir = Path(local_download_path)
                local_dir.mkdir(parents=True, exist_ok=True)
                # os.makedirs(local_download_path, exist_ok=True)
        
                # Extract the file name from the input path
                file_name = Path(s3_key).name
                local_file_path = local_dir / file_name

                async with self.get_s3_client() as s3_client:
                    try:
                        # Download the file from S3 to the local path
                        async with aiofiles.open(local_file_path, 'wb') as local_file:
                            await s3_client.download_fileobj(
                                Bucket=self.bucket_name,
                                Key=s3_key,
                                Fileobj=local_file
                            )
                        return [str(local_file_path)]
                    except Exception as error:
                        logger.exception(f"S3 download error: {error}")
                        raise HTTPException(
                            status_code=HTTP_STATUS_INTERNAL_ERROR,
                            detail=MSG_S3_DOWNLOAD_FAILED
                        )
            except Exception as s3_error:
                logger.exception(f"Local setup error: {s3_error}")
                raise HTTPException(status_code = HTTP_STATUS_INTERNAL_ERROR,
                                    detail = MSG_S3_FETCH_ERROR)
            


    async def download_semantic_model_input_files(
        self,
        s3_input_prefix: str,
        local_download_path: str,
        allowed_extensions: List[str] = [".twb", ".csv", ".xlsx"]
    ) -> List[str]:
        """
        Downloads all semantic model input files (filtered by extension) from a given S3 prefix to local path.

        Args:
            s3_input_prefix (str): The S3 folder/prefix (e.g., org/email/process_id/input_files)
            local_download_path (str): Local path to store the files.
            allowed_extensions (List[str], optional): File types to download. Defaults to common types.

        Returns:
            List[str]: List of downloaded local file paths.
        """
        try:
            os.makedirs(local_download_path, exist_ok=True)
            downloaded_files = []

            async with self.get_s3_client() as s3_client:
                response = await s3_client.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix=s3_input_prefix.rstrip("/") + "/"
                )

                if "Contents" not in response or not response["Contents"]:
                    logger.warning(f"No files found at S3 prefix: {s3_input_prefix}")
                    return []

                for obj in response["Contents"]:
                    file_key = obj["Key"]
                    file_name = os.path.basename(file_key)

                    # Skip if not a file or if the extension isn't allowed
                    if not file_name or (allowed_extensions and Path(file_name).suffix.lower() not in allowed_extensions):
                        continue

                    local_file_path = os.path.join(local_download_path, file_name)

                    try:
                        # Stream download to local
                        async with aiofiles.open(local_file_path, "wb") as f:
                            response_stream = await s3_client.get_object(Bucket=self.bucket_name, Key=file_key)
                            async for chunk in response_stream["Body"]:
                                await f.write(chunk)
                        downloaded_files.append(local_file_path)
                        logger.info(f"Downloaded {file_key} to {local_file_path}")

                    except Exception as e:
                        logger.error(f"Failed to download {file_key}: {e}")

            return downloaded_files

        except Exception as err:
            logger.exception(f"Failed to download semantic input files from {s3_input_prefix}: {err}")
            raise HTTPException(status_code=500, detail="Failed to download semantic model input files from S3")



class OpenAIConfig(BaseSettings):
    api_key: str = os.getenv("OPENAI_API_KEY")

    def get_openai_client(self):
        return OpenAI(api_key=self.api_key)


class DBConfig:
    db_uri = os.getenv("DB_URI")

    @classmethod
    def get_engine(cls):
        return create_engine(cls.db_uri)


class JWTConfig(BaseSettings):
    secret_key: str = os.getenv("JWT_SECRET_KEY")
    algorithm: str = "HS256"


ACCESS_TOKEN_EXPIRY = int(os.getenv("ACCESS_TOKEN_EXPIRY", 500))  # in minutes
REFRESH_TOKEN_EXPIRY = int(os.getenv("REFRESH_TOKEN_EXPIRY", 60 * 3)) # in minutes
