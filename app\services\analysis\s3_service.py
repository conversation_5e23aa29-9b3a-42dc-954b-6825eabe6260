import uuid
import json
import os
from pathlib import Path
import shutil
import aiofiles
from fastapi import HTTPException, status
from botocore.exceptions import ClientError
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from botocore.exceptions import EndpointConnectionError
from typing import Tuple, List, Dict, Any
from starlette.concurrency import run_in_threadpool
from uuid import UUID

from app.core.config import S3Config
from app.core import INPUT_FILES_DIR
from app.core.logger_setup import logger
from app.models.report_details import ReportDetail, ReportDetailManager




s3_config = S3Config()
bucket_name = s3_config.bucket_name



async def download_twb_files_from_s3_by_id(report_id: UUID, user) -> Tuple[str, str, str, Dict]:
    """
    Downloads and converts a .twb or .twbx file from S3 for the given report_id.
    Uses hardcoded S3 path: BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{report_name}.twb/twbx
    Returns local paths for further processing.
    """
    try:
        report = await run_in_threadpool(ReportDetailManager.get_report_by_id, report_id)
        if not report:
            raise HTTPException(status_code=404, detail=f"Report with ID {report_id} not found")

        report_name = report.name.strip()
        # Use current user's organization name exactly as given (no strip)
        organization_name = user.organization.name
        s3_report_id = report.report_id
       

        # Extract base name without extension if it exists
        # Some report names in DB already have .twb or .twbx extension
        if report_name.lower().endswith(('.twb', '.twbx')):
            base_report_name = os.path.splitext(report_name)[0]
        else:
            base_report_name = report_name

        # Use the exact S3 path structure as specified by user: BI-PortV3/{org_name}/{report_id}
        s3_key = None

        # Primary paths using user's specified structure
        # Use organization name exactly as given
        primary_paths = [
            f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{report_name}.twbx",
            f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{report_name}.twb",
            f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{report_name}",
            f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{base_report_name}.twbx",
            f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{base_report_name}.twb",
            f"BI-PortV3/{organization_name}/{s3_report_id}/tableau_file/{base_report_name}",
           
        ]

        async with s3_config.get_s3_client() as s3_client:
            # Try the primary paths first
            for key in primary_paths:
                try:
                    await s3_client.head_object(Bucket=bucket_name, Key=key)
                    s3_key = key
                    logger.info(f"Found file at: {s3_key}")
                    break
                except ClientError as e:
                    if e.response['Error']['Code'] != "404":
                        raise e

            if not s3_key:
                raise HTTPException(
                    status_code=404,
                    detail=f"No .twb/twbx file found for report {report_name} at BI-PortV3/{organization_name}/{s3_report_id}/ path structure. Tried: {', '.join(primary_paths)}"
                )

        # Updated local directory structure: ./storage/{organization_name}/{s3_report_id}/
        # Sanitize organization name for local file paths (remove spaces and special chars)
        local_org_name = organization_name.strip().replace(" ", "_")
        local_dir = os.path.join("./storage", local_org_name, str(s3_report_id))
        twb_file_dir = os.path.join(local_dir, INPUT_FILES_DIR)
        os.makedirs(twb_file_dir, exist_ok=True)

        # Convert the TWB or TWBX file to txt format
        input_dir = await convert_twb_to_txt_s3(bucket_name, [s3_key], twb_file_dir)
        logger.info(f"[S3Helper] Converted TWB/TWBX file for report_id {report_id}: {input_dir}")

        # Return unique_folder for S3 operations using user's specified path structure
        unique_folder = f"BI-PortV3/{organization_name}/{s3_report_id}"
        return input_dir, local_dir, unique_folder, {}

    except Exception as e:
        logger.error(f"Error in download_twb_files_from_s3_by_id: {e}", exc_info=True)
        raise

async def zip_output_files(folder_name: str, unique_folder: str, folder_path: str, local_dir: str) -> Tuple[str, str]:
    """
    Create zip of output files and upload to S3.
    Uses hardcoded S3 path: BI-PortV3/{organization_name}/{s3_report_id}/analyzed_output/{report_name}.zip

    Args:
        folder_name (str): Name of the folder to zip (report_name).
        unique_folder (str): Target S3 folder (BI-PortV3/{organization_name}/{s3_report_id}).
        folder_path (str): Local path to zip.
        local_dir (str): Base directory for zip file.

    Returns:
        Tuple[str, str]: Presigned URL and zip file name.
    """
    # Create zip file locally
    zip_path = shutil.make_archive(base_name=os.path.join(local_dir, folder_name), format='zip', root_dir=folder_path)
    zip_file = Path(zip_path)

    # Hardcoded S3 path for analyzed output: BI-PortV3/{organization_name}/{s3_report_id}/analyzed_outputs/{report_name}.zip
    zip_s3_key = f"{unique_folder}/analyzed_outputs/{folder_name}.zip"

    await s3_config.upload_to_s3(file_path=str(zip_path), object_name=zip_s3_key)
    zip_file_name = zip_file.name
    download_url = await s3_config.generate_presigned_url(object_key=zip_s3_key)

    return download_url, zip_file_name


  


def sanitize_for_json(data: Any, seen: set = None) -> Any:
    """
    Recursively convert objects to JSON-compatible formats and handle circular references.

    Args:
        data (Any): The object to sanitize.
        seen (set, optional): Internal tracker for circular refs.

    Returns:
        Any: Sanitized object.
    """
    if seen is None:
        seen = set()

    if id(data) in seen:
        return "[Circular Reference]"
    seen.add(id(data))

    if isinstance(data, dict):
        return {key: sanitize_for_json(value, seen) for key, value in data.items()}
    elif isinstance(data, list):
        return [sanitize_for_json(item, seen) for item in data]
    elif isinstance(data, (str, int, float, bool)) or data is None:
        return data
    else:
        return str(data)



async def convert_twb_to_txt_s3(bucket_name, s3_keys, local_files_directory):
    """
    Convert TWB/TWBX files from S3 and save TXT files in an 'input_files' subdirectory locally.

    Args:
        bucket_name (str): The name of the S3 bucket.
        s3_keys (list): List of S3 keys (paths) of the TWB/TWBX files.
        local_files_directory (str): The local directory where converted files will be stored.

    Returns:
        str: Path to the local directory containing the converted TXT files.
    """
    # Ensure the local directory exists
    os.makedirs(local_files_directory, exist_ok=True)

    if not s3_keys:
        logger.warning("No S3 keys provided.")
        return local_files_directory

    # Process each S3 key
    for s3_key in s3_keys:
        try:
            # Extract and sanitize filename from S3 key
            filename = os.path.basename(s3_key)
            filename = os.path.normpath(filename).replace("..", "")  # Sanitize

            if not (filename.endswith(".twb") or filename.endswith(".twbx")):
                logger.warning(f"Skipping non-TWB/TWBX file: {filename}")
                continue

            # Define local path
            local_twb_path = os.path.join(local_files_directory, filename)
            logger.info(f"Preparing to download from S3: Bucket={bucket_name}, Key={s3_key}")
            logger.info(f"Local path for download: {local_twb_path}")

            # Download file from S3
            try:
                s3_client = await S3Config().get_s3_client()  # Ensure the client is properly closed when the function exits
                await s3_client.download_file(bucket_name, s3_key, local_twb_path)  # Ensure the client is properly closed when the function exits
                logger.info(f"Downloaded file to: {local_twb_path}")
                await s3_client.__aexit__(None, None, None)  # Properly close the client when done with the file
            except (NoCredentialsError, PartialCredentialsError) as e:
                logger.error(f"S3 authentication error: {e}")
                continue
            except EndpointConnectionError as e:
                logger.error(f"Connection error to S3 endpoint: {e}")
                continue
            except Exception as e:
                logger.error(f"Unexpected error during S3 client operation: {e}")
                continue

            # Read and write the content of the TWB/TWBX file
            try:
                with open(local_twb_path, 'r', encoding='utf-8') as file:
                    content = file.read()

                # Convert to TXT and save locally
                if filename.endswith('.twbx'):
                    txt_filename = filename.replace('.twbx', '.txt')
                else:
                    txt_filename = filename.replace('.twb', '.txt')
                txt_file_path = os.path.join(local_files_directory, txt_filename)

                with open(txt_file_path, 'w', encoding='utf-8') as txt_file:
                    txt_file.write(content)
                logger.info(f"Converted {filename} to TXT: {txt_file_path}")
            except IOError as e:
                logger.error(f"File I/O error: {e}")
                continue  # Skip this file and move to the next
        
        except Exception as e:
            logger.error(f"Unexpected error processing file")
    return local_files_directory
