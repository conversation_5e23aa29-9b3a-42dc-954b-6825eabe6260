import json
from .get_singlevisual_data  import get_config_json, get_uuid
from app.services.migrate.Tableau_Analyzer.report.worksheet import process_worksheets
from app.services.migrate.Tableau_Analyzer.report.dashboard import process_worksheets_in_dashboards
from app.core.enums import PowerBIReportKeys, PowerBITemplateKeys, TableauXMLTags
from .dashboard import get_dashboard_visual_container_visuals
from app.services.migrate.visuals.text_box import process_text_boxes_in_dashboard


def generate_report_sections(root, chart_types):
    tableau_analyzed_worksheets_data = process_worksheets(root, chart_types)
    sections_list = []
    generated_visuals = {}
    for ordinal_num, visual_details in enumerate(tableau_analyzed_worksheets_data, start=1):
        config_json = {}
        visual_containsers = []
        powerbi_visual_json = get_config_json(visual_details)
        config_json[PowerBIReportKeys.CONFIG.value] = PowerBIReportKeys.EMPTY_DICTIONARY.value
        config_json[PowerBIReportKeys.DISPLAY_NAME.value] = visual_details.get(PowerBITemplateKeys.WORKSHEET_NAME.value)
        config_json[PowerBIReportKeys.DISLAY_OPTION.value] = 1
        config_json[PowerBIReportKeys.FILTERS.value] = PowerBIReportKeys.EMPTY_SQUARE.value
        config_json[PowerBIReportKeys.HEIGHT.value] = 720.00
        config_json[PowerBIReportKeys.NAME.value] = get_uuid()
        config_json[PowerBIReportKeys.ORDINAL.value] = ordinal_num
        visual_containsers.append({PowerBIReportKeys.CONFIG.value:json.dumps(powerbi_visual_json)})
        generated_visuals[visual_details.get(PowerBITemplateKeys.WORKSHEET_NAME.value)] = powerbi_visual_json
        config_json[PowerBIReportKeys.VISUAL_CONTAINERS.value] = visual_containsers
        config_json[PowerBIReportKeys.WIDTH.value] = 1280.00
        sections_list.append(config_json)
    tableau_analyzed_dashboard_data = process_worksheets_in_dashboards(root)
    text_boxes_in_dashboards = process_text_boxes_in_dashboard(root.findall(TableauXMLTags.DASHBOARD.value))

    """ Dashboard processing started here """
    for dashboard_name, ws_dimentions_data in tableau_analyzed_dashboard_data.items():
        config_json = {}
        visual_containsers = []
        config_json[PowerBIReportKeys.CONFIG.value] = PowerBIReportKeys.EMPTY_DICTIONARY.value
        config_json[PowerBIReportKeys.DISPLAY_NAME.value] = dashboard_name
        config_json[PowerBIReportKeys.DISLAY_OPTION.value] = 1
        config_json[PowerBIReportKeys.FILTERS.value] = PowerBIReportKeys.EMPTY_SQUARE.value
        config_json[PowerBIReportKeys.HEIGHT.value] = 720.00
        config_json[PowerBIReportKeys.NAME.value] = get_uuid()
        config_json[PowerBIReportKeys.ORDINAL.value] = ordinal_num
        visual_containsers = get_dashboard_visual_container_visuals(generated_visuals, ws_dimentions_data)
        text_box_visuals = text_boxes_in_dashboards.get(dashboard_name)
        dashboard_text_boxes = [{PowerBIReportKeys.CONFIG.value: json.dumps(each_visual)} for each_visual in text_box_visuals]
        visual_containsers.extend(dashboard_text_boxes)
        config_json[PowerBIReportKeys.VISUAL_CONTAINERS.value] = visual_containsers
        config_json[PowerBIReportKeys.WIDTH.value] = 1280.00
        sections_list.append(config_json)
        ordinal_num += 1
    return sections_list
