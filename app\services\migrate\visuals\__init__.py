from app.services.migrate.visuals.bar import process_bar_chart
from .tree_map import get_treemap_report, process_tree_map_report
from .filled_map import get_filledmap_report, process_filled_map_report
from .card import get_card_report
from .table import get_table_report
from .pie_chart import get_piechart_report, process_pie_chart_report
from .slicer import get_slicer_report
from .text_box import process_text_box_in_worksheets,get_title_textbox
from .line_chart import get_linechart_report, process_line_chart_report
from .map import get_map_report
from .line_stacked import get_line_stacked_report
from .clustered_column_chart import get_barchart_report
from .scatterplot import get_scatter_plot, process_scatter_chart_report
from .pivot_table import get_highlight_report
from .line_bar import get_line_bar_chart_report
from .areachart import get_areachart_report, process_area_chart_report
from .column_chart import get_stacked_bar_report
from app.core import *
from .page_navigation import get_page_navigation_report
from .bar_or_column_chart import get_stacked_bar_horizontal_report
from .table import process_table_ex_report
from .map import process_map_report
from .line_stacked import process_line_stacked_column_combo_chart_report
from .line_bar import process_line_bar_chart_report
from .pivot_table import process_pivot_table_report
from .text_box import process_text_box_in_worksheets

from app.core.enums import  ChartType



visuals_generation_mapping = {
    ChartType.LINE.value: process_line_chart_report,
    ChartType.AREA.value:  process_area_chart_report,
    ChartType.SCATTER.value: process_scatter_chart_report,
    ChartType.HORIZONTAL_BAR.value: process_bar_chart,
    ChartType.VERTICAL_BAR.value: process_bar_chart,
    ChartType.STACKED_HORIZONTAL_BAR.value: process_bar_chart,
    ChartType.STACKED_VERTICAL_BAR.value:  process_bar_chart,
    ChartType.TREE_MAP.value: process_tree_map_report,
    ChartType.FILLED_MAP.value: process_filled_map_report,
    ChartType.PIE.value: process_pie_chart_report,
    ChartType.TEXT_TABLE.value: process_table_ex_report,
    ChartType.SYMBOL_MAP.value: process_map_report,
    ChartType.BAR_AND_AREA.value: process_line_stacked_column_combo_chart_report,
    ChartType.HIGHLIGHTED_TABLE.value: process_pivot_table_report,
    ChartType.BAR_AND_LINE.value: process_line_bar_chart_report,
    ChartType.TEXT_BOX.value: process_text_box_in_worksheets
}