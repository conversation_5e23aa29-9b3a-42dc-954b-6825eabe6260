from uuid import UUID
from typing import List, Dict

from .analysis_services import AnalysisService
from app.core import ServiceResponse
from app.core.exceptions import ServerError

# class AnalysisProcessor:



class AnalysisProcessor:

    @staticmethod
    async def analyse_processor(report_id: UUID, user) -> ServiceResponse:
        """
        Processes analysis request for a given report ID.
        - If already analyzed, return pre-signed URL.
        - If not, perform analysis and then return pre-signed URL.
        - Also includes hierarchical project path.

        Args:
            report_id (UUID): ID of the report to analyze.
            user: Logged-in user performing the action.

        Returns:
            ServiceResponse: with download URL and project/report path structure.
        """
        try:
            response = await AnalysisService().analysing_report(report_id, user)
            return ServiceResponse(
                data=response,
                error=None,
                status_code=200
            )
        except Exception as e:
            return ServiceResponse(
                data=None,
                error=str(e),
                status_code=500
            )
