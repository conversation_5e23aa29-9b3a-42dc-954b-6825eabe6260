from dotenv import load_dotenv
import json, xmltodict, re, uuid
from app.core.templates import *
from app.core.logger_setup import logger
from ...core import COLOR_PALETTE_DATA

def fix_xml_line(xml_line):
    return re.sub(r"formula=x'([^']*)'", r'formula="\1"', xml_line)

def extract_json(file_path):
    try:
        with open(file_path, encoding='utf-8') as xml_file:
            # Read and fix the content
            content = xml_file.read()
            fixed_content = fix_xml_line(content)
            
            # Parse the fixed content
            data_dict = xmltodict.parse(fixed_content)
        return data_dict
    except Exception as e:
        logger.error(f"---- Error in converting xml to json --- {str(e)}")
        raise ValueError(f"Error in converting xml to json - {str(e)}")

def get_filter_column(data):
    match = re.search(r'\[(\w+):([^\]]+):\w+\]', data)
    return match.group(1), match.group(2)

def find_mode(zones, search_params):
    results = []
    
    def traverse(zone):
        if isinstance(zone, dict):
            if "@param" in zone and zone["@param"] == search_params:
                results.append(zone.get("@mode"))
            for key, value in zone.items():
                if isinstance(value, (dict, list)):
                    traverse(value)
        elif isinstance(zone, list):
            for item in zone:
                traverse(item)
    traverse(zones)
    return results[0] if len(results)!=0 else None

def fetch_zone_attributes(zones, search_string):
    results = []
    def search_zones(zone):
        if isinstance(zone, dict):
            # Check if the `@param` key exists and contains the search string
            if "@param" in zone and search_string in zone["@param"]:
                # param_filter, param_col = get_filter_column(zone["@param"])
                # if param_col == search_string:
                    result = {
                        "@x": zone.get("@x"),
                        "@y": zone.get("@y"),
                        "@w": zone.get("@w"),
                        "@h": zone.get("@h"),
                        "@mode": zone.get("@mode"),
                        "zone_style": zone.get("zone-style")
                    }
                    results.append(result)

            # Recursively search nested zones
            if "zone" in zone:
                if isinstance(zone["zone"], list):
                    for sub_zone in zone["zone"]:
                        search_zones(sub_zone)
                elif isinstance(zone["zone"], dict):
                    search_zones(zone["zone"])

    # Start the search
    search_zones(zones)
    return results[0]

def fetch_border_color(zone_style):
    if zone_style:
        if "format" in zone_style:
            for item in zone_style.get("format",[]):
                if item.get("@attr") == "border-color":
                    return item.get("@value")

def find_text_type_zones(zones):
    text_zones = []
    for zone in zones:
        # Check if the zone is a text zone
        if isinstance(zone,dict):
            if zone.get("@type-v2") == "text" and "formatted-text" in zone:
                run = zone["formatted-text"]["run"]
                text_zones.append({
                    "id": zone.get("@id"),
                    "text": run.get("#text", ""),
                    "font_size": run.get("@fontsize", "default"),
                    "font_color": run.get("@color", "default"),
                    "font_style": run.get("@fontstyle", "normal"),
                    "bold": run.get("@bold", "false") == "true",
                    "dimensions": {
                        "h": zone.get("@h"),
                        "w": zone.get("@w"),
                        "x": zone.get("@x"),
                        "y": zone.get("@y"),
                    }
                })
            if "zone" in zone:
                nested_zones = zone["zone"]
                if isinstance(nested_zones, list):
                    text_zones.extend(find_text_type_zones(nested_zones))
                else:
                    text_zones.extend(find_text_type_zones([nested_zones]))
    return text_zones[0] if len(text_zones)!=0 else None

def get_tables_data(data):
    try:
        table_column_data = {}
        datasources = data.get('workbook', {}).get('datasources', {}).get('datasource')  
        # Check if datasources is a list or a dictionary
        if isinstance(datasources, list):
            datasource_list = datasources
        else:
            datasource_list = [datasources]
        for datasource in datasource_list:
            metadata_records = datasource.get('connection', {}).get('metadata-records', {}).get('metadata-record')
            if metadata_records:
                for record in metadata_records:
                    if record.get('@class') == "column":
                        table_name = record.get('parent-name', '').strip('[]')
                        column_name = record.get('local-name', '').strip('[]')
                        if table_name in table_column_data:
                            table_column_data[table_name].append(column_name)
                        else:
                            table_column_data[table_name] = [column_name]
        return table_column_data
    except Exception as e:
        logger.error(f"---- Error in getting table column data --- {str(e)}")
        raise ValueError(f"Error in getting table column data - {str(e)}")

def is_column_present(table_column_data, column_name):
    return next(((True, table) for table, columns in table_column_data.items() if column_name in columns), (False, None))

def get_slicer_calc(columns, name):
    """
    Fetch the dictionary from the column list where '@name' matches the given string.

    :param columns: List of dictionaries containing column data.
    :param name: String to match with the '@name' key.
    :return: The dictionary where '@name' matches the given string or None if not found.
    """
    for column in columns:
        if name in column.get("@name"):
            return column

def find_tables_with_columns(table_column_data, columns_to_match):
    matching_tables = []
    
    # Iterate through each table and its columns in the dictionary  
    for table, columns in table_column_data.items():
        # Check if all columns to match are present in the table's columns
        if all(column in columns for column in columns_to_match):
            matching_tables.append(table)
    
    return matching_tables

def get_calculation_data(calc_column, column_list):
    for col in column_list:
        if calc_column in col.get('@name'):
            calc_caption = col.get('@caption')
            calc_formula = col.get('calculation').get('@formula')

            return calc_caption, calc_formula

def get_calc_filter_column(data, table_column_data, datasource_col_list):
    filter_value, col_value = get_filter_column(data)
    is_present, table_name = is_column_present(table_column_data, col_value)
    if not is_present:
        column_value, calc_formula  = get_calculation_data(col_value, datasource_col_list)
        calc_column_list = get_formula_columns(calc_formula)
        calc_table_name = resolve_table_name(calc_column_list, table_column_data, datasource_col_list)
        return filter_value, column_value, calc_table_name
    return filter_value, col_value, table_name

def resolve_table_name(calc_column_list, table_column_data, datasource_col_list):
    table_name = find_tables_with_columns(table_column_data, calc_column_list)
    if table_name:
        return table_name[0]
    else:
        col_value = calc_column_list[0]
        column_value, calc_formula  = get_calculation_data(col_value, datasource_col_list)
        calc_column_list = get_formula_columns(calc_formula)
        return resolve_table_name(calc_column_list, table_column_data, datasource_col_list)

def get_select_function(filter_value):
    if filter_value in ['sum', 'cnt', 'usr']:
        return 0
    elif filter_value in ['avg']:
        return 1
    elif filter_value in ['max']:
        return 4
    return 0

def get_select_json(column, filter, table_name, queryref):
    native_ref_name = get_nativeReferenceName(filter, column)
    if 'Calculation' in column:
        return {"Measure": {"Expression": {"SourceRef": {"Source": table_name}},"Property": column},"Name": queryref,"NativeReferenceName": native_ref_name}
    if filter in ['sum', 'cnt', 'usr', 'avg', 'max']:
        return {"Aggregation": {"Expression": {"Column": {"Expression": {"SourceRef": {"Source": table_name}},"Property": column}},"Function": get_select_function(filter)},"Name": queryref,"NativeReferenceName": native_ref_name}
    elif filter in ["yr","tyr"]:
        return {"HierarchyLevel":{"Expression":{"Hierarchy":{"Expression":{"PropertyVariationSource":{"Expression":{"SourceRef":{"Source":table_name}},"Name":"Variation","Property":column}},"Hierarchy":"Date Hierarchy"}},"Level":"Year"},"Name":queryref,"NativeReferenceName":f"{column} Year"}
    elif filter in ["mn","tmn"]:
        return {"HierarchyLevel":{"Expression":{"Hierarchy":{"Expression":{"PropertyVariationSource":{"Expression":{"SourceRef":{"Source":table_name}},"Name":"Variation","Property":column}},"Hierarchy":"Date Hierarchy"}},"Level":"Month"},"Name":queryref,"NativeReferenceName":f"{column} Month"}
    elif filter in ["qr","tqr"]:
        return {"HierarchyLevel":{"Expression":{"Hierarchy":{"Expression":{"PropertyVariationSource":{"Expression":{"SourceRef":{"Source":table_name}},"Name":"Variation","Property":column}},"Hierarchy":"Date Hierarchy"}},"Level":"Quarter"},"Name":queryref,"NativeReferenceName":f"{column} Quarter"}
    elif filter in ["dy","tdy"]:
        return {"HierarchyLevel":{"Expression":{"Hierarchy":{"Expression":{"PropertyVariationSource":{"Expression":{"SourceRef":{"Source":table_name}},"Name":"Variation","Property":column}},"Hierarchy":"Date Hierarchy"}},"Level":"Day"},"Name":queryref,"NativeReferenceName":f"{column} Day"}
    return {"Column": {"Expression": {"SourceRef": {"Source": table_name}},"Property": column},"Name": queryref,"NativeReferenceName": native_ref_name}

def get_queryref(filter_value, column_value, table_name):
    if filter_value in ['yr',"tyr"]:
        query_ref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Year"
    elif filter_value in ["mn","tmn"]:
        query_ref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Month" 
    elif filter_value in ["sum", "usr"]:
        query_ref = f"Sum({table_name}.{column_value})"
    elif filter_value in ["qr","tqr"]:
        query_ref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Quarter"
    elif filter_value in ["dy","tdy"]:
        query_ref=f"{table_name}.{column_value}.Variation.Date Hierarchy.Day"
    elif filter_value in ["cnt"]:
        query_ref = f'Count({table_name}.{column_value})'
    elif filter_value in ["avg"]:
        query_ref = [(f'Average({table_name}.{column_value})', filter_value)]
    elif filter_value in ["max"]:
        query_ref = [(f'Max({table_name}.{column_value})', filter_value)]
    else:
        query_ref = f"{table_name}.{column_value}"
    return query_ref


def get_queryref_data(filter_value, column_value, table_name):
    year_queryref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Year"
    month_queryref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Month"
    quarter_queryref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Quarter"
    day_queryref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Day"
    
    if filter_value in ['yr',"tyr"]:
        query_ref = [(year_queryref, 'yr')]
    elif filter_value == 'qr':
        query_ref = [(quarter_queryref, 'qr')]
    elif filter_value == 'mn':
        query_ref = [(month_queryref, 'mn')] 
    elif filter_value == 'dy':
        query_ref = [(day_queryref, 'dy')]
    elif filter_value == 'tqr':
        query_ref = [(year_queryref, 'yr'), (quarter_queryref, 'qr')]
    elif filter_value in ['tmn', 'twk']:
        query_ref = [(year_queryref, 'yr'), (quarter_queryref, 'qr'), (month_queryref, 'mn')]
    elif filter_value == 'tdy':
        query_ref = [(year_queryref, 'yr'), (quarter_queryref, 'qr'), (month_queryref, 'mn'), (day_queryref, 'dy')]
    elif filter_value in ["sum", "usr"]:
        query_ref = [(f"Sum({table_name}.{column_value})", filter_value)]
    elif filter_value in ["cnt"]:
        query_ref = [(f'Count({table_name}.{column_value})', filter_value)]
    elif filter_value in ["avg"]:
        query_ref = [(f'Average({table_name}.{column_value})', filter_value)]
    elif filter_value in ["max"]:
        query_ref = [(f'Max({table_name}.{column_value})', filter_value)]
    else:
        query_ref = [(f"{table_name}.{column_value}", filter_value)]
    return query_ref

def get_nativeReferenceName(filter_value, column_value):
    if 'yr' in filter_value:
        native_reference_name = f'{column_value} Year'
    elif 'mn' in filter_value:
        native_reference_name = f'{column_value} Month'
    elif 'sum' in filter_value:
        native_reference_name = f'Sum of {column_value}'
    elif 'cnt' in filter_value:
        native_reference_name = f'Count of {column_value}'
    else:
        native_reference_name = column_value
    return native_reference_name

def is_linestackedcolumn_chart(extracted_panes_list, required_panes_list):
    if all(item in extracted_panes_list for item in required_panes_list):
        remaining_values = [item for item in extracted_panes_list if item not in required_panes_list]
        if all(value == 'Automatic' for value in remaining_values):
            return True
    return False

def process_title_layout(run_data):
    if isinstance(run_data, list):
        res = {}
        for item in run_data:
            if isinstance(item, dict):
                for key, value in item.items():
                    if key == "#text":
                        res[key] = res.get(key, "") + value  # Concatenating text values
                    else:
                        res.setdefault(key, value) 
        return res
    return run_data

def find_zones_by_name(zone, names):
    result = []
    if isinstance(zone, dict):
        if zone.get('@name') in names  and '@mode' not in zone and '@param' not in zone:
            result.append(zone)
        for key, value in zone.items():
            if isinstance(value, dict) or isinstance(value, list):
                result.extend(find_zones_by_name(value, names))
    elif isinstance(zone, list):
        for item in zone:
            result.extend(find_zones_by_name(item, names))
    return result

def calculate_dimensions(x, y, h, w):
    return {
        "height": float(h) * 720.00 / 100000.00,
        "width": float(w) * 1280.00 / 100000.00,
        "x": float(x) * 1280.00 / 100000.00,
        "y": float(y) * 720.00 / 100000.00,
        "z": 0.00
    }

def get_dimensions(zones, ws_name, worksheet_names):
    zones_data = find_zones_by_name(zones, worksheet_names)
    result_zone = next((value for value in zones_data if value.get('@name') == ws_name), None)
    if result_zone:
        dimension_json = calculate_dimensions(result_zone.get('@x',0), result_zone.get('@y',0), result_zone.get('@h',0), result_zone.get('@w',0))
        return dimension_json
    return {}

def generate_json_from_styles(style, run):
    # Extract background color from style-rule where element = 'title'
    background_color = None
    if style:
        style_rule_element = style.get('style-rule')
        style_rule_element = style_rule_element if isinstance(style_rule_element, list) else [style_rule_element]
        for rule in style_rule_element:
            if rule.get("@element") == "title":
                for format_item in rule.get("format", []):
                    if not isinstance(format_item, dict): continue
                    if format_item.get("@attr") == "background-color":
                        background_color = format_item.get("@value")
                        break
                if background_color:
                    break

    # Generate the JSON structure using run data and extracted background color
    title_output = {}

    if "#text" in run:
        title_output["text"] = {
            "expr": {
                "Literal": {
                    "Value": f"'{run.get('#text')}'"
                }
            }
        }

    if "@fontname" in run:
        font_name = run.get("@fontname")
        font_family = "Calibri" if "tableau" in font_name.lower() else font_name
        title_output["fontFamily"] = {
            "expr": {
                "Literal": {
                    "Value": f"'{font_family}'"
                }
            }
        }

    if "@bold" in run:
        title_output["bold"] = {
            "expr": {
                "Literal": {
                    "Value": str(run.get("@bold", "false")).lower()
                }
            }
        }

    if "@italic" in run:
        title_output["italic"] = {
            "expr": {
                "Literal": {
                    "Value": str(run.get("@italic", "false")).lower()
                }
            }
        }

    if "@underline" in run:
        title_output["underline"] = {
            "expr": {
                "Literal": {
                    "Value": str(run.get("@underline", "false")).lower()
                }
            }
        }

    if "@fontcolor" in run:
        title_output["fontColor"] = {
            "solid": {
                "color": {
                    "expr": {
                        "Literal": {
                            "Value": f"'{run.get('@fontcolor')}'"
                        }
                    }
                }
            }
        }

    if background_color:

        title_output["background"] = {
            "solid": {
                "color": {
                    "expr": {
                        "Literal": {
                            "Value": f"{background_color}"
                        }
                    }
                }
            }
        }

    if "@fontalignment" in run:
        alignment_map = {"1": "center", "2": "right"}
        alignment_value = alignment_map.get(run.get("@fontalignment"), "left")
        title_output["alignment"] = {
            "expr": {
                "Literal": {
                    "Value": f"'{alignment_value}'"
                }
            }
        }

    if "@fontsize" in run:
        title_output["fontSize"] = {
            "expr": {
                "Literal": {
                    "Value": f"{run.get('@fontsize')}D"
                }
            }
        }

    return title_output

def get_title_config(worksheet_title_layout, style):
    if worksheet_title_layout:
        run_data = process_title_layout(worksheet_title_layout)
        title_json = generate_json_from_styles(style, run_data)
        title_list = [{"properties": {"show": {"expr": {"Literal": {"Value": "true"}}}, **title_json}}]
            
    else:
        title_list = [{"properties": {"show": {"expr": {"Literal": {"Value": "false"}}}}}]

    return title_list

def get_background_config(style = None, zones_format = None):
    background_color = None
    if style:
        style_rule_element = style.get('style-rule')
        style_rule_element = style_rule_element if isinstance(style_rule_element, list) else [style_rule_element]
        for rule in style_rule_element:
            if rule.get("@element") == "table":
                format_items = rule.get("format")
                format_items = format_items if isinstance(format_items, list) else [format_items]
                for format_item in format_items:
                    if format_item.get("@attr") == "background-color":
                        background_color = format_item.get("@value")
                        break
                if background_color:
                    break
    if zones_format:
        zones_format = zones_format if isinstance(zones_format, list) else [zones_format]
        for zone in zones_format:
            if zone.get("@attr") == "background-color":
                background_color = zone.get("@value")
                break
    if background_color:
        return [{"properties": {"color": {"solid": {"color": {"expr": {"Literal": {"Value": f"'{background_color}'"}}}}},"show": {"expr": {"Literal": {"Value": "true"}}}}}]
    else:
        return [{"properties": { "show": {"expr": {"Literal": {"Value": "false"}}}}}]
      
def get_border_config(zones_format = None):
    border_color = None
    if zones_format:
        zones_format = zones_format if isinstance(zones_format, list) else [zones_format]
        for zone in zones_format:
            if zone.get("@attr") == "border-color":
                border_color = zone.get("@value")
                break
    if border_color:
        return json.dumps([{"properties": {"show": {"expr": {"Literal": {"Value": "true"}}},"color": {"solid": {"color": {"expr": {"Literal": {"Value": f"'{border_color}'"}}}}}}}])
    else:
        return json.dumps([{"properties": {"show": {"expr": {"Literal": {"Value": "false"}}}}}])

def get_queryref_list(filter_value, column_value, table_name):
    query_ref_list=[]
    if filter_value in ["yr","tyr"]:
        query_ref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Year"
        query_ref_list.append(query_ref) 
    elif filter_value in ["mn","tmn"]:
        query_ref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Month"
        query_ref_list.append(query_ref)
    elif filter_value == 'sum':
        query_ref = f"{filter_value.title()}({table_name}.{column_value})"
        query_ref_list.append(query_ref)
    elif filter_value in ["qr","tqr"]:
        query_ref = f"{table_name}.{column_value}.Variation.Date Hierarchy.Quarter"
        query_ref_list.append(query_ref)
    else:
        query_ref = f"{table_name}.{column_value}"
        query_ref_list.append(query_ref)
    return query_ref_list

def highlightchart_filedsget(queryref,table_column_data, datasource_column_list):
    queryref_list=get_queryref_list(queryref,table_column_data,datasource_column_list)
    return queryref_list

def get_colors_(val_quref,table_column_data,data_source_column_list):
    fil_name,col_name,tab_name=get_calc_filter_column(val_quref,table_column_data,data_source_column_list)
    color_json_list=[{"properties":{"backColor":{"solid":{"color":{"expr":{"FillRule":{"Input":{"Aggregation":{"Expression":{"Column":{"Expression":{"SourceRef":{"Entity":f"{tab_name}"}},"Property":f"{col_name}"}},"Function":0}},"FillRule":{"linearGradient3":{"min":{"color":{"Literal":{"Value":"'#FFFFFF'"}},"value":{"Literal":{"Value":"0D"}}},"mid":{"color":{"Literal":{"Value":"'#BBE4D8'"}},"value":{"Literal":{"Value":"1D"}}},"max":{"color":{"Literal":{"Value":"'#2D5A86'"}},"value":{"Literal":{"Value":"14000D"}}},"nullColoringStrategy":{"strategy":{"Literal":{"Value":"'asZero'"}}}}}}}}}}},"selector":{"data":[{"dataViewWildcard":{"matchingOption":1}}],"metadata":f"{fil_name.title()}({tab_name}.{col_name})"}},{"properties":{"bandedRowHeaders":{"expr":{"Literal":{"Value":"false"}}},"fontSize":{"expr":{"Literal":{"Value":"13D"}}}}}]
    return color_json_list

def get_column_properties_json(col_filtervalue,col_name,tab_name):
    if col_filtervalue=="sum":
        return {f"Sum({tab_name}.{col_name})":{f"displayName":f"{col_name}"}}
    else:
        return ""

def get_level(col_fil):
    if col_fil in ["yr","tyr"]:
        return "Year"
    elif col_fil in ["qr","tqr"]:
        return "Quarter"
    elif col_fil in ["mn","tmn"]:
        return "Month"
    elif col_fil in ["dy","tdy"]:
        return "Day"
    else :
        return None

def query_ref_list(string,table_column_data,datasource_column_list,qreflist):
    fil_name,col_name,table_name=get_calc_filter_column(string,table_column_data,datasource_column_list)
    heirarchy_levels=['dy','mn','qr','yr']
    heirarchy_index={'dy':0,'mn':1,'qr':2,'yr':3}
    if fil_name in ["tyr","tqr","tmn","tdy"]:
        data_heirarchy=heirarchy_levels[heirarchy_index[fil_name[1:]]:][::-1]
        for filname_h in data_heirarchy:
            qreflist.append({"queryRef":f"{get_queryref(filname_h,col_name,table_name)}","active":True})
    else:
        qreflist.append({"queryRef":f"{get_queryref(fil_name,col_name,table_name)}","active":True})
    return qreflist,table_name

def get_select_json_list(fil_name,col_name,tab_name,select_json_list):
    heirarchy_levels=['dy','mn','qr','yr']
    heirarchy_index={'dy':0,'mn':1,'qr':2,'yr':3}
    if fil_name in ["tyr","tqr","tmn","tdy"]:
        data_heirarchy=heirarchy_levels[heirarchy_index[fil_name[1:]]:][::-1]
        for filname_h in data_heirarchy:
            query_ref_of_bi=get_queryref(filname_h,col_name,tab_name)
            select_json_list.append(get_select_json(col_name,filname_h,tab_name,query_ref_of_bi))
    else:
        query_ref_of_bi=get_queryref(fil_name,col_name,tab_name)
        select_json_list.append(get_select_json(col_name,fil_name,tab_name,query_ref_of_bi))
    return select_json_list

def get_from_list(table_names):
    return [{"Name": table_name,"Entity": table_name,"Type": 0} for table_name in table_names]

def get_order_by_list(daatstring,tablecolumndata,datasource_list,direction_val):
    orderbylist=[]
    fil_val,col_val,tab_name=get_calc_filter_column(daatstring,tablecolumndata,datasource_list)
    if fil_val in  ["sum"]:
        orderbylist.append({"Direction":direction_val,"Expression":{"Aggregation":{"Expression":{"Column":{"Expression":{"SourceRef":{"Source":f"{tab_name}"}},"Property":f"{col_val}"}},"Function":0}}})
    if fil_val in  ["cnt"]:
        orderbylist.append({"Direction":direction_val,"Expression":{"Aggregation":{"Expression":{"Column":{"Expression":{"SourceRef":{"Source":f"{tab_name}"}},"Property":f"{col_val}"}},"Function":4}}})
    return orderbylist

def get_fields_data(data):
    queryref_string_list=re.findall(r'\[.*?\].\[.*?\]', data) if data else []
    return queryref_string_list

def get_columns_in_formula(formula):
    formula_fileds = re.findall(r'\[.*?\]', formula)
    return formula_fileds
def get_projections_data(projections_data, table_column_data, datasource_col_list):
    table_list, select_list = [], []
    result_queryref, select_queryref_dict = {}, {}
    for data in projections_data:
        queryref_list = []
        if data:
            queryref_data = get_fields_data(data)
            for field in queryref_data:
                field_filter, field_column, field_table_name = get_calc_filter_column(field, table_column_data, datasource_col_list)
                field_queryref_list = get_queryref_data(field_filter, field_column, field_table_name)
                for queryref_filter in field_queryref_list:
                    queryref = queryref_filter[0]
                    filter_value = queryref_filter[1]
                    queryref_list.append({"queryRef":queryref,"active":True})
                    if queryref not in select_queryref_dict: 
                        select_queryref_dict[queryref] = [field_column, filter_value, field_table_name, queryref]
                if field_table_name not in table_list: table_list.append(field_table_name)
            result_queryref[data] = queryref_list

    for data in select_queryref_dict.values():
        select_list.append(get_select_json(data[0], data[1], data[2], data[3]))

    return result_queryref, table_list, select_list

def to_list(data):
    return data if isinstance(data, list) else [data]

def process_multiple_fields(tooltip_data):
    if tooltip_data:
        tooltip_data = to_list(tooltip_data)
        return " + ".join(item for item in tooltip_data)

def process_tooltip_data(data, color_field = None, visual_fields = None):

    visual_fields_list = []

    if visual_fields:
        for field in visual_fields:
            if field:
                visual_fields_list.extend(get_fields_data(field))

    combined_tooltips = [item for lst in data for item in lst]
    tooltip_list = [tooltip.get("@column") for tooltip in combined_tooltips]
    unique_tooltip_data = list(dict.fromkeys(tooltip_list))
    if color_field: unique_tooltip_data.append(color_field)
    final_tooltip_data = [data for data in unique_tooltip_data if data not in visual_fields_list]

    tooltips_data = process_multiple_fields(final_tooltip_data)
    return tooltips_data

def extract_color_encoding(style_rule):
    if style_rule:
        for item in style_rule:
            if item.get("@element") == "mark":
                for encoding in to_list(item.get("encoding")):
                    if encoding.get("@attr") == "color":
                        return encoding

def process_values_input(values, table_column_data, datasource_column_list):
    filter_value, column_value, table_name = get_calc_filter_column(values, table_column_data,datasource_column_list)
    value_queryref = get_queryref(filter_value, column_value, table_name)
    values_data = {"Aggregation": {"Expression": {"Column": {"Expression": {"SourceRef": {"Entity": table_name}},"Property": column_value}},"Function": 0}}
    if filter_value in ['sum']:
        values_data = {"Aggregation": {"Expression": {"Column": {"Expression": {"SourceRef": {"Entity": table_name}},"Property": column_value}},"Function": 0}}
    else:
        values_data = {"Column": {"Expression": {"SourceRef": {"Entity": table_name}},"Property": column_value},"Function":0}
    return values_data, value_queryref

def get_color_formatting(style, values, table_column_data, datasource_column_list):

    values_input, value_queryref = process_values_input(values, table_column_data, datasource_column_list)
    min_color, mid_color, max_color = '#BCE4D8', '#45a2b9', '#2C5985'
    if style:
        style_rule = style.get("style-rule")
        style_rule = to_list(style_rule)
        result = extract_color_encoding(style_rule)
        if result:
            if "color-palette" in result:
                colors = result.get("color-palette",{}).get("color")
                if colors:
                    min_color, mid_color, max_color = colors[0], colors[len(colors)//2], colors[(len(colors)-1)]
            if "@palette" in result:
                palette_name = result.get("@palette")
                if palette_name:
                    color_data = COLOR_PALETTE_DATA.get(palette_name)
                    min_color, mid_color, max_color = color_data.get("Min"), color_data.get("Mid"), color_data.get("Max")

    return values_input, value_queryref, min_color, mid_color, max_color

def get_color_field(panes):
    color_values = [pane.get("encodings").get("color") for pane in panes if "encodings" in pane and "color" in pane.get("encodings")]
    if color_values:
        color_data = color_values[0]
        return color_data.get("@column")

def process_pane_encodings(panes):
    panes = to_list(panes)
    tooltip_list, lod_list, text_list = [],[],[]
    color = get_color_field(panes)
    for pane in panes:
        tooltip_data = pane.get("encodings",{}).get("tooltip")
        lod_data = pane.get("encodings",{}).get("lod")
        text_data = pane.get("encodings",{}).get("text")
        if tooltip_data:
            tooltip_data = to_list(tooltip_data)
            tooltip_list.extend(tooltip_data)
        if lod_data:
            lod_data = to_list(lod_data)
            lod_list.extend(lod_data)
        if text_data:
            text_data = to_list(text_data)
            text_list.extend(text_data)
    return color, tooltip_list, lod_list, text_list

def get_format_data(format_data, field, scope):
    result_value = None
    if format_data:
        for data in format_data:
            if data.get("@field") == field:
                result_value = data.get("@value")
            elif data.get("@scope") == scope:
                result_value = data.get("@value")
            elif not result_value:
                result_value = data.get("@value")
    return result_value

def handle_format_data(format_data, field=None, scope=None):
    color_list, font_style_list, font_weight_list, text_decoration_list, font_size_list = [], [], [], [], []
    format_list = to_list(format_data)
    for item in format_list:
        if item.get("@attr") == "color":
            color_list.append(item)
        if item.get("@attr") == "font-style":
            font_style_list.append(item)
        if item.get("@attr") == "font-weight":
            font_weight_list.append(item)
        if item.get("@attr") == "text-decoration":
            text_decoration_list.append(item)
        if item.get("@attr") == "font-size":
            font_size_list.append(item)
    color = get_format_data(color_list, field, scope)
    font_style = get_format_data(font_style_list, field, scope)
    font_weight = get_format_data(font_weight_list, field, scope)
    text_decoration = get_format_data(text_decoration_list, field, scope)
    font_size = get_format_data(font_size_list, field, scope)
    return color, font_style, font_weight, text_decoration, font_size

def create_expr(value):
    return {"expr": {"Literal": {"Value": value}}}

def process_format_data(style_rule, field_data, scope):
    properties_dict = {}
    properties_dict["switchAxisPosition"] = create_expr("false")
    properties_dict["labelPrecision"] = create_expr("1L")
    properties_dict["showAxisTitle"] = create_expr("true")
    properties_dict["show"] = create_expr("true")
    
    style_rule = to_list(style_rule)
    for rule in style_rule:
        element = rule.get("@element")
        format_data = rule.get("format")
        if element == "label" and format_data:
            color, font_style, font_weight, text_decoration, font_size = handle_format_data(format_data, field_data, scope)
            if color:
                properties_dict["titleColor"] = {"solid":{"color":create_expr(f"'{color}'")}}
                properties_dict["labelColor"] = {"solid":{"color":create_expr(f"'{color}'")}}
            if font_style and font_style != "normal":
                properties_dict["titleItalic"] = create_expr("true")
                properties_dict["italic"] = create_expr("true")
            if font_weight and font_weight != "normal":
                properties_dict["titleBold"] = create_expr("true")
                properties_dict["bold"] = create_expr("true")
            if text_decoration and text_decoration != "none":
                properties_dict["titleUnderline"] = create_expr("true")
                properties_dict["underline"] = create_expr("true")
            if font_size:
                properties_dict["titleFontSize"] = create_expr(f"{font_size}D")
                properties_dict["fontSize"] = create_expr(f"{font_size}D")
            
    return properties_dict

def process_label_data(style_rule):
    label_properties_dict = {}
    label_properties_dict["show"] = create_expr("true")
    if style_rule:
        for rule in style_rule:
            element = rule.get("@element")
            format_data = rule.get("format")
            if element == "cell" and format_data:
                color, font_style, font_weight, text_decoration, font_size = handle_format_data(format_data=format_data)
                if color:
                    label_properties_dict["color"] = {"solid":{"color":create_expr(f"'{color}'")}}
                if font_style and font_style != "normal":
                    label_properties_dict["italic"] = create_expr("true")
                if font_weight and font_weight != "normal":
                    label_properties_dict["bold"] = create_expr("true")
                if text_decoration and text_decoration != "none":
                    label_properties_dict["underline"] = create_expr("true")
                if font_size:
                    label_properties_dict["fontSize"] = create_expr(f"{font_size}D")
                else:
                    label_properties_dict["fontSize"] = create_expr("14D")
    return label_properties_dict

def get_text_box_zones_in_dashboards(zones):
    text_zones = []
    for zone in zones.get("zone", []):
        if zone.get("@type-v2") == "text":
            text_zone = {
                "x": zone.get("@x"),
                "y": zone.get("@y"),
                "w": zone.get("@w"),
                "h": zone.get("@h"),
                "text_value": zone.get("formatted-text", {}).get("run", {}).get("#text"),
                "font_size": zone.get("formatted-text", {}).get("run", {}).get("@fontsize"),
                "text_color": "#000000"
            }
            text_zones.append(text_zone)
    return text_zones

# def get_tablue_id_work_sheet_names(dashboards, windows):
#     dashboard_buttons = {}
#     button_id_worksheet_name = {}
#     for dashboard in dashboards:
#         zones = dashboard.findall(".//zone")
#         for zone in zones:
#             button_element = zone.find("button")
#             if button_element is not None and button_element.get("action"):
#                 match = re.findall(r'\"(.*?)\"', button_element.get("action"))
#                 if match:
#                     dashboard_buttons[match[0]] = True
#     for window in windows:
#         simple_id = window.find("simple-id")
#         uuid = simple_id.get("uuid") if simple_id is not None else None
#         name = window.get("name")
#         if uuid and uuid in dashboard_buttons:
#             button_id_worksheet_name[uuid] = name
#     return button_id_worksheet_name

def get_tablue_id_work_sheet_names(dashboards,windows):
    dashboard_buttons=[]
    button_id_worksheet_name={}
    dashboards=dashboards if isinstance(dashboards,list) else [dashboards]
    for dashboard in dashboards:
        zones=dashboard.get("zones",{}).get("zone",{})
        zones=zones if isinstance(zones,list) else [zones]
        for zone in zones:
            if not "button" in zone.keys():
                continue
            dashboard_buttons.append(re.findall(r'\"(.*?)\"',zone.get("button",{}).get("@action",{}))[0])
    windows= windows.get("window") if isinstance(windows.get("window"),list) else [windows.get("window")]
    for window in windows:
        if window.get("simple-id",{}).get("@uuid",{}) in dashboard_buttons:
            button_id_worksheet_name[window.get("simple-id",{}).get("@uuid",{})]=window.get("@name")
    return button_id_worksheet_name

def get_name_of_report_name_id(worksheet_data):
    report_name_uuid_name_in_power_bi={}
    for key,value in worksheet_data.items():
        report_name_uuid_name_in_power_bi[key]=value[0].get("config",{}).get("visual_config_name","")
    return report_name_uuid_name_in_power_bi

def get_report_name_id_inpbi(section_list):
    report_name_id_in_pbi={}
    for visual in section_list:
        report_name_id_in_pbi[visual["displayName"]]=visual["name"]
    return report_name_id_in_pbi

def get_dashboard_sheets(root):
    dashboards = root.findall(".//dashboard")  # Updated to use XML format
    dashboards_worksheets_names = {}
    for dashboard in dashboards:
        dashboard_name = dashboard.get("name", "")
        worksheets = []
        zones = dashboard.findall("zones/zone")  # Fetch zones from dashboard
        for zone in zones[1:]:  # Skipping first element
            zone_name = zone.get("name")  # Extract sheet name from the zone
            if zone_name:
                worksheets.append(zone_name)
        dashboards_worksheets_names[dashboard_name] = worksheets
    return dashboards_worksheets_names

# This definition is created  for handling name errors where tables starting with same name
def update_table_source_tags(updated_table_tags,tab_name):
    tag_suffix=0
    if not tab_name in updated_table_tags.keys():
        if tab_name[0].lower() in updated_table_tags.values():
            while tab_name[0].lower()+str(tag_suffix+1) in updated_table_tags.values():
                tag_suffix+=1
            updated_table_tags[tab_name]=tab_name[0].lower()+str(tag_suffix)
        else:
            updated_table_tags[tab_name]=tab_name[0].lower()
    return updated_table_tags

# In Customised tool_tips the below function return the target_name : source and details of the title
def target_source_in_tooltips(data):
    worksheet_name_tooltips={}
    worksheets=data.get("workbook").get("worksheets").get("worksheet")
    worksheets=worksheets if isinstance(worksheets,list) else [worksheets]
    for worksheet in worksheets:
        wn=worksheet.get("@name")
        name=worksheet.get("table").get("panes",{}).get("pane",{}).get("customized-tooltip",{}).get("formatted-text",{})
        if not name: continue
        worksheet_name_tooltips[wn]={}
        if isinstance(name.get("run"),str):
            name_value = re.search(r'name="([^"]+)"', name.get("run","")).group(1)
            worksheet_name_tooltips[wn]["target_name"]=name_value
        elif isinstance(name.get("run"),list):
            for tooltip_details in name.get("run"):
                if isinstance(tooltip_details,dict):
                    worksheet_name_tooltips[wn]["is_bold"]=tooltip_details.get("@bold","false")
                    worksheet_name_tooltips[wn]["title_name"]=tooltip_details.get("#text","")
                elif isinstance(tooltip_details,str):
                    name_value=re.search(r'name="([^"]+)"', tooltip_details).group(1)
                    worksheet_name_tooltips[wn]["target_name"]=name_value
                else:
                    pass
    return worksheet_name_tooltips

def get_config_value_of_main():
    return {"version":"5.59","themeCollection":{"baseTheme":{"name":"CY24SU10","version":"5.61","type":2}},"activeSectionIndex":0,"defaultDrillFilterOtherVisuals":True,"linguisticSchemaSyncVersion":2,"settings":{"useNewFilterPaneExperience":True,"allowChangeFilterTypes":True,"useStylableVisualContainerHeader":True,"queryLimitOption":6,"useEnhancedTooltips":True,"exportDataMode":1,"useDefaultAggregateDisplayName":True},"objects":{"section":[{"properties":{"verticalAlignment":{"expr":{"Literal":{"Value":"'Top'"}}}}}],"outspacePane":[{"properties":{"expanded":{"expr":{"Literal":{"Value":"false"}}}}}]}}

def get_tooltip_targets(worksheets_tooltips):
    source_tooltips=[]
    for target,det in worksheets_tooltips.items():
        source_tooltips.append(det.get("target_name"))
    return source_tooltips