import xml.etree.ElementTree as ET
from typing import List, Dict, Any
import re
from sqlalchemy import ColumnDefault
from app.core.enums import GeneralKeys, PowerBITemplateKeys, TableauXMLTags
from app.services.migrate.core import get_fields_data

def extract_style_data(style_element: ET.Element) -> Dict[str, Dict[str, List[Dict[str, str]]]]:
    styles: Dict[str, Dict[str, List[Dict[str, str]]]] = {}

    for rule in style_element.findall(TableauXMLTags.STYLE_RULE.value):
        element_name = rule.get(TableauXMLTags.ELEMENT.value)
        if not element_name:
            continue

        if element_name not in styles:
            styles[element_name] = {}

        for child in rule:
            tag = child.tag
            if tag not in styles[element_name]:
                styles[element_name][tag] = []
            styles[element_name][tag].append(child.attrib)
    return styles

def extract_worksheet_title(title_run_data: ET.Element) -> List[Dict[str, str]]:
    run_data = []

    for run in title_run_data:
        run_dict = {"text": run.text or ""}
        run_dict.update(run.attrib)
        run_data.append(run_dict)

    return run_data

def extract_panes_data(panes: ET.Element) -> List[Dict[str, Any]]:
    pane_list = []

    for pane in panes:
        mark = pane.find(TableauXMLTags.MARK.value)
        mark_class = mark.get(TableauXMLTags.CLASS.value)

        encodings = pane.find(TableauXMLTags.ENCODINGS.value)
        pane_encoding = {}

        if encodings:
            for encoding in encodings:
                tag = encoding.tag
                column_val = encoding.get(TableauXMLTags.COLUMN.value)
                if tag not in pane_encoding:
                    pane_encoding[tag] = []
                pane_encoding[tag].append(column_val)

        styles = pane.find(TableauXMLTags.STYLE.value)
        pane_style = extract_style_data(styles) if styles else None

        pane_list.append({
            "mark_class": mark_class,
            "pane_encoding": pane_encoding,
            "pane_style": pane_style
        })

    return pane_list

def extract_encodings_data(panes: ET.Element, tag_to_extract: str) -> List[str]:
    """
    Extracts column values from specific encoding tags inside all <pane> elements.

    :param panes_element: XML element containing multiple <pane> tags
    :param tag_to_extract: The tag name (e.g., 'tooltip', 'color') to look for inside <encodings>
    :return: A list of 'column' attribute values from matching tags
    """
    
    column_values = []
    for pane in panes:
        encodings = pane.find(TableauXMLTags.ENCODINGS.value)
        if encodings is None:
            continue

        for encoding_tag in encodings.findall(tag_to_extract):
            column = encoding_tag.get(TableauXMLTags.COLUMN.value)
            if column:
                column_values.append(column)
    return column_values

def extract_multiple_encodings_data(panes: ET.Element, tags_to_extract: List[str]) -> Dict[str, List[str]]:
    """
    Extracts column values for multiple encoding tags (e.g., 'tooltip', 'color') from <encodings> in all <pane> elements.

    :param panes: XML element containing multiple <pane> tags
    :param tags_to_extract: List of encoding tag names to extract
    :return: Dictionary where each key is a tag and the value is a list of column names
    """
    result = {tag: [] for tag in tags_to_extract}

    for pane in panes:
        encodings = pane.find(TableauXMLTags.ENCODINGS.value)
        if encodings is None:
            continue

        for tag in tags_to_extract:
            for encoding_tag in encodings.findall(tag):
                column = encoding_tag.get(TableauXMLTags.COLUMN.value)
                if column:
                    result[tag].append(column)

    return result



def extract_datasource_columns(datasource_dependencies: ET.Element) -> List:
    result = []
    for datasource_dep in datasource_dependencies:
        datasource_data = {
            "datasource_name": datasource_dep.attrib.get(TableauXMLTags.DATASOURCE_NAME.value, 'N/A'),
            "column": []
        }
        
        for column in datasource_dep.findall(TableauXMLTags.COLUMN.value):
            column_data = {key: column.attrib[key].strip("[]") for key in column.attrib}
            
            calculation = column.find(TableauXMLTags.CALCULATION.value)
            if calculation is not None:
                column_data.update({key: calculation.attrib[key] for key in calculation.attrib})
            
            datasource_data[TableauXMLTags.COLUMN.value].append(column_data)
        
        result.append(datasource_data)

    return result

def extract_table_columns(datasources: ET.Element) -> List:
    result_data = []
    for datasource in datasources:
        table_columns = {}
        metadata_records = datasource.findall(TableauXMLTags.METADATA_RECORDS.value)
        if metadata_records:
            for metadata_record in metadata_records:
                parent_name = metadata_record.findtext(TableauXMLTags.PARENT_NAME.value).strip("[]")
                column_name = metadata_record.findtext(TableauXMLTags.REMOTE_NAME.value) \
                                or metadata_record.findtext(TableauXMLTags.LOCAL_NAME.value)
                data_type = metadata_record.findtext(TableauXMLTags.LOCAL_TYPE.value, "none")
                aggregation = metadata_record.findtext(TableauXMLTags.AGGREGATION.value, "none")
                table_columns.setdefault(parent_name, [])
                table_columns[parent_name].append({
                    GeneralKeys.COLUMN_NAME_KEY.value : column_name, 
                    "data_type": data_type,
                    TableauXMLTags.AGGREGATION.value: aggregation})

            result_data.append({
                "datasource": datasource.attrib.get(TableauXMLTags.NAME.value, "Unknown"),
                PowerBITemplateKeys.TABLE_COLUMNS.value: table_columns})
    return result_data

def get_formula_columns(input_string):
    return re.findall(r'\[(.*?)\]', input_string)

def extract_calcuations_related_details(table_column_data, datasource_columns):
    calculations_related_data = {}
    for each_datasource_column_detail in datasource_columns:
        if TableauXMLTags.COLUMN.value in each_datasource_column_detail.keys():
            for each_column in each_datasource_column_detail.get(GeneralKeys.COLUMN.value):
                if GeneralKeys.CALCULATION_.value in each_column.get(TableauXMLTags.NAME.value) and GeneralKeys.FORMULA.value in each_column.keys():
                    details = {}
                    selected_column = get_formula_columns(each_column.get(GeneralKeys.FORMULA.value))[0]
                    for each_datasource_table_column_data in table_column_data:
                        if PowerBITemplateKeys.TABLE_COLUMNS.value in each_datasource_table_column_data.keys():
                            for table_name, column_details in each_datasource_table_column_data.get(PowerBITemplateKeys.TABLE_COLUMNS.value).items():
                                for column_detail in column_details:
                                    if column_detail.get(GeneralKeys.COLUMN_NAME_KEY.value)==selected_column:
                                        details[PowerBITemplateKeys.COLUMN_VALUE.value] =selected_column
                                        details[TableauXMLTags.AGGREGATION.value] = column_detail.get(TableauXMLTags.AGGREGATION.value)
                                        details[PowerBITemplateKeys.TABLE_NAME.value] = table_name
                                        calculations_related_data[each_column.get(TableauXMLTags.NAME.value)] = details
    return calculations_related_data