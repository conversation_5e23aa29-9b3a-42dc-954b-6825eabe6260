from .Tableau_Analyzer.report.worksheet import process_worksheets
from .dashboard import get_dashboard_json
import uuid
from app.core.logger_setup import logger
# from .visuals.page_navigation import get_page_navigation_report
# from .core import get_tablue_id_work_sheet_names,get_report_name_id_inpbi,get_dashboard_sheets
# from .visuals.drill_throgh import get_drill_through_from_root

def get_report_json(root, chart_types):
    section_list = process_worksheets(root, chart_types)
    section_list = []
    for i, item in enumerate(worksheet_data, start = 1):
        section_list.append(
            {
                "config" : "{}",
                "displayName" : item,
                "displayOption" : 1,
                "filters": "[]",
                "height": 720.00,
                "name": f"{str(uuid.uuid4()).replace('-','')[:20]}",
                "ordinal": i,
                "visualContainers": worksheet_data[item],
                "width": 1280.00
            }
        )
    logger.info(f"==========Completed worksheet processing============")
    dashboard_data = get_dashboard_json(root, chart_types,)

    for item in dashboard_data:
        section_list.append(
            {
                "config" : "{}",
                "displayName" : item.get('dashboard_name'),
                "displayOption" : 1,
                "filters": "[]",
                "height": 720.00,
                "name": f"{str(uuid.uuid4()).replace('-','')[:20]}",
                "visualContainers": item.get('visual_container'),
                "width": 1280.00
            }
        )
    logger.info(f"============Completed dashboard processing==========")
    return section_list