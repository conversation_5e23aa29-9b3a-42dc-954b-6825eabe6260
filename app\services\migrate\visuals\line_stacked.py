from ..core import get_background_config, get_border_config, get_calc_filter_column, get_from_list, get_queryref, get_select_json, get_title_config
from app.core import line_stacked_column_json
from app.core import logger
import uuid, json
from app.core.enums import *
from app.services.migrate.Tableau_Analyzer.report import (
    remove_duplicate_fields,
    extract_multiple_encodings_data,
    generate_projections_data
)


def get_line_stacked_report(panes, category_col, table_column_data, datasource_col_list, worksheet_name, worksheet_title_layout, style):
    try:
        overall_linestacked_result = []
        y_queryref_data, y2_queryref_data = None, None
        tables_list, select_list = [], []
        for pane in panes:
            mark_class = pane.get('mark',{}).get('@class')
            if mark_class and mark_class == 'Bar':
                y_queryref_data = pane.get('encodings',{}).get(TableauXMLTags.TEXT.value,{}).get('@column')

            if mark_class and mark_class == 'Area':
                y2_queryref_data = pane.get('encodings',{}).get(TableauXMLTags.TEXT.value,{}).get('@column')

        if y_queryref_data and y2_queryref_data:
            y_filter_value, y_col_value, y_table_name = get_calc_filter_column(y_queryref_data, table_column_data, datasource_col_list)
            y2_filter_value, y2_col_value, y2_table_name = get_calc_filter_column(y2_queryref_data, table_column_data, datasource_col_list)
            category_filter, category_column, category_table = get_calc_filter_column(category_col, table_column_data, datasource_col_list)
            y_queryref = get_queryref(y_filter_value, y_col_value, y_table_name)
            y2_queryref = get_queryref(y2_filter_value, y2_col_value, y2_table_name)
            category_queryref = get_queryref(category_filter, category_column, category_table)
            if y_table_name not in tables_list: tables_list.append(y_table_name)
            if y2_table_name not in tables_list: tables_list.append(y2_table_name)
            from_list = get_from_list(tables_list)
            select_list.append(get_select_json(y_col_value, y_filter_value, y_table_name, y_queryref))
            select_list.append(get_select_json(y2_col_value, y2_filter_value, y2_table_name, y2_queryref))
            select_list.append(get_select_json(category_column, category_filter, category_table, category_queryref))
            title_list = get_title_config(worksheet_title_layout, style)
            background_list = get_background_config(style= style)
            line_stacked_json = {
                "visual_config_name" : f'{str(uuid.uuid4()).replace("-","")[:20]}',
                "y_queryref" : y_queryref,
                "y2_queryref" : y2_queryref,
                "category_queryref" : category_queryref,
                "from_list" : json.dumps(from_list),
                "select_list" : json.dumps(select_list),
                "col_prop_displayname" : y_col_value,
                "title_list" : json.dumps(title_list),
                "background_list" : json.dumps(background_list),
                "border_list" : get_border_config()
            
            }
            overall_linestacked_result.append({"config": line_stacked_json, "template": line_stacked_column_json})

            return overall_linestacked_result
    except Exception as e:
        logger.error(f"---Error in generating line stacked column visual for {worksheet_name}--- {str(e)}")
        raise ValueError(f"Error in generating line stacked column visual for {worksheet_name} - {str(e)}")


def get_secondary_axis(style_data):
    '''retrieves the value of 'field' from 'encoding' tag in styles for secondary axis'''

    return style_data.get(TableauXMLTags.AXIS.value, {}).get(TableauXMLTags.ENCODING.value, {}).get(TableauXMLTags.FIELD.value)



def process_line_stacked_column_combo_chart_report(request: VisualRequest):
    """
    Process line stacked column combo chart visual request.

    Parameters
    ----------
    request : VisualRequest
        The request object containing details for the line stacked column combo chart.

    Returns
    -------
    list
        A list containing the processed line stacked column combo chart data.
    """
    line_stacked_column_combo_chart = {}
    panes = request.panes
    rows = request.rows
    cols = request.cols
    table_column_data = request.table_column_data
    style_data = request.worksheet_style_data
    calculations_related_data = request.calculations_related_data
    encodings = extract_multiple_encodings_data(
        panes=panes,
        tags_to_extract=[
            TableauXMLTags.TOOLTIP.value,
            TableauXMLTags.LOD.value,
            TableauXMLTags.TEXT.value,
            TableauXMLTags.COLOR.value
        ]
    )

    unique_tooltips = remove_duplicate_fields(
        encodings[TableauXMLTags.TOOLTIP.value],
        encodings[TableauXMLTags.LOD.value],
        encodings[TableauXMLTags.TEXT.value],
        rows,
        cols
    )
    unique_color_data = remove_duplicate_fields(encodings[TableauXMLTags.COLOR.value])
    secondary_axis = get_secondary_axis(style_data)

    line_stackd_column_combo_chart_field_mapping = {
        PowerBIReportKeys.Y.value: rows,
        PowerBIReportKeys.CATEGORY.value: cols,
    }

    if unique_tooltips:
        line_stackd_column_combo_chart_field_mapping[PowerBIReportKeys.TOOLTIPS.value] = unique_tooltips
    if not secondary_axis and unique_color_data:
        line_stackd_column_combo_chart_field_mapping[PowerBIReportKeys.SERIES.value] = unique_color_data
    if secondary_axis:
        line_stackd_column_combo_chart_field_mapping[PowerBIReportKeys.Y2.value] = [secondary_axis]

    projections_data = generate_projections_data(table_column_data, calculations_related_data, line_stackd_column_combo_chart_field_mapping)

    line_stacked_column_combo_chart[PowerBITemplateKeys.VISUAL_TYPE.value] = PowerBIChartTypes.LINE_CHART.value
    line_stacked_column_combo_chart[PowerBITemplateKeys.WORKSHEET_NAME.value] = request.worksheet_name
    line_stacked_column_combo_chart[PowerBITemplateKeys.PROJECTIONS_DATA.value] = projections_data

    return line_stacked_column_combo_chart