from pydantic import BaseModel, EmailStr, StringConstraints, Field
from typing import Optional, Annotated
from uuid import UUID


class UserCreate(BaseModel):
    name: Annotated[str, StringConstraints(strip_whitespace=True, min_length=2, max_length=50)]
    email: EmailStr
    organizationName: Annotated[str, StringConstraints(strip_whitespace=True, min_length=2, max_length=100)]
    phoneNumber: Annotated[str, StringConstraints(pattern=r'^\d{10}$')]
    designation: Annotated[str, StringConstraints(strip_whitespace=True, min_length=2, max_length=50)]
    password: Optional[Annotated[str, StringConstraints(min_length=8, max_length=64)]]
    numberOfReports: Optional[int] = Field(default=None, ge=0, le=1000)

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class ResetPassword(BaseModel):
    password: str

class RefreshToken(BaseModel):
    refresh_token: str
    email: str

class AddUserRequest(BaseModel):
    name: Annotated[str, StringConstraints(strip_whitespace=True, min_length=2, max_length=50)]
    email: EmailStr
    password: Annotated[str, StringConstraints(min_length=8, max_length=64)]
    phone_number: Annotated[str, StringConstraints(pattern=r'^\d{10}$')]
    organization_id: str
    role_id: str
    manager_id: Optional[str] = None
